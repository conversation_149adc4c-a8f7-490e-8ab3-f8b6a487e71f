import { createClient } from "@supabase/supabase-js";
import { UserProfile } from "../../../shared/types";

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export const supabase = createClient(supabaseUrl, supabaseServiceKey);

export class SupabaseService {
  async createUserProfile(
    userId: string,
    email: string,
    name?: string
  ): Promise<UserProfile> {
    const { data, error } = await supabase
      .from("users")
      .insert({
        id: userId,
        email,
        name,
        subscription_tier: "Free",
        credits_remaining: 10,
        is_active: true,
      })
      .select()
      .single();

    if (error || !data) {
      throw new Error(error?.message ?? "Failed to create user profile");
    }

    return data;
  }

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      if ((error as any).code === "PGRST116") return null;
      throw new Error(error.message);
    }

    return data;
  }

  async updateUserProfile(
    userId: string,
    updates: Partial<UserProfile>
  ): Promise<UserProfile> {
    const { data, error } = await supabase
      .from("users")
      .update(updates)
      .eq("id", userId)
      .select()
      .single();

    if (error || !data) {
      throw new Error(error?.message ?? "Failed to update user profile");
    }

    return data;
  }

  // Verify JWT token and get user
  async verifyToken(token: string) {
    return await supabase.auth.getUser(token);
  }
}

export const supabaseService = new SupabaseService();
