import axios from 'axios';
import { Flashcard, QuizQuestion, DifficultyLevel, ContentLength } from '../../../shared/types';

export class AIService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  private readonly model = 'mistralai/mistral-small-3.2-24b-instruct:free';

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY!;
    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }
  }

  async generateFlashcards(
    content: string,
    count: number = 10,
    customPrompt?: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): Promise<Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>[]> {
    const prompt = this.buildFlashcardPrompt(content, count, customPrompt, difficultyLevel, contentLength);

    try {
      const response = await this.callOpenRouter(prompt);
      return this.parseFlashcardResponse(response, difficultyLevel, contentLength);
    } catch (error) {
      throw new AIError('Flashcard generation failed', error);
    }
  }

  async generateQuizQuestions(
    content: string,
    count: number = 10,
    customPrompt?: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): Promise<Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[]> {
    let attempts = 0;
    const maxAttempts = 2;

    while (attempts < maxAttempts) {
      attempts++;

      // Reduce question count on retry to avoid truncation
      const adjustedCount = attempts > 1 ? Math.max(5, Math.floor(count * 0.7)) : count;
      const prompt = this.buildQuizPrompt(content, adjustedCount, customPrompt, difficultyLevel, contentLength);

      try {
        console.log(`Quiz generation attempt ${attempts} with ${adjustedCount} questions`);
        const response = await this.callOpenRouter(prompt);
        console.log(`Received response length: ${response.length} characters`);

        const questions = this.parseQuizResponse(response, difficultyLevel, contentLength);
        console.log(`Successfully parsed ${questions.length} questions`);

        if (questions.length > 0) {
          return questions;
        }

        throw new Error('No valid questions generated');
      } catch (error) {
        console.error(`Quiz generation attempt ${attempts} failed:`, error instanceof Error ? error.message : error);

        if (attempts >= maxAttempts) {
          throw new AIError('Quiz generation failed after multiple attempts', error);
        }

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new AIError('Quiz generation failed after all attempts');
  }

  private buildFlashcardPrompt(
    content: string,
    count: number,
    customPrompt?: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): string {
    // Generate difficulty-specific instructions
    const difficultyInstructions = this.getDifficultyInstructions(difficultyLevel);

    // Generate content length-specific instructions
    const lengthInstructions = this.getContentLengthInstructions(contentLength);

    const basePrompt = `
Create exactly ${count} high-quality flashcards from the following content. Each flashcard should have a clear, concise front (question/term) and a comprehensive back (answer/definition).

DIFFICULTY LEVEL: ${difficultyLevel.toUpperCase()}
${difficultyInstructions}

CONTENT LENGTH: ${contentLength.toUpperCase()}
${lengthInstructions}

General Guidelines:
- Focus on key concepts, definitions, and important facts
- Make questions specific and answerable
- Vary question types (definitions, explanations, examples, applications)
- Use clear, educational language

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

CRITICAL: Return ONLY a valid JSON array with this exact structure:
[
  {
    "front": "Question or term here",
    "back": "Answer or definition here",
    "difficulty_level": "${difficultyLevel}",
    "content_length": "${contentLength}"
  }
]

IMPORTANT:
- Generate exactly ${count} flashcards
- Ensure the JSON is properly formatted and complete
- End with a proper closing bracket ]
- Do not include any text before or after the JSON array`;

    return basePrompt;
  }

  private getDifficultyInstructions(difficultyLevel: DifficultyLevel): string {
    switch (difficultyLevel) {
      case DifficultyLevel.EASY:
        return `- Create simple, straightforward questions focusing on basic facts and definitions
- Use clear, simple language that's easy to understand
- Focus on fundamental concepts and key terms
- Avoid complex reasoning or multi-step problems`;

      case DifficultyLevel.MEDIUM:
        return `- Create moderately challenging questions that require understanding of concepts
- Include some application of knowledge and basic reasoning
- Balance between facts and conceptual understanding
- Use standard academic language`;

      case DifficultyLevel.HARD:
        return `- Create challenging questions that require deep understanding
- Include analysis, synthesis, and application of multiple concepts
- Use complex scenarios and require critical thinking
- Expect advanced vocabulary and technical terms`;

      case DifficultyLevel.COLLEGE:
        return `- Create college-level questions appropriate for undergraduate study
- Require comprehensive understanding of subject matter
- Include analytical thinking and problem-solving
- Use academic terminology and expect detailed knowledge`;

      case DifficultyLevel.GRADUATE:
        return `- Create graduate-level questions for advanced study
- Require sophisticated analysis and synthesis of complex concepts
- Include research-level understanding and critical evaluation
- Use specialized terminology and expect expert-level knowledge`;

      case DifficultyLevel.PHD:
        return `- Create PhD-level questions for doctoral research
- Require cutting-edge understanding and original thinking
- Include complex theoretical frameworks and advanced methodologies
- Use highly specialized terminology and expect research-level expertise`;

      default:
        return this.getDifficultyInstructions(DifficultyLevel.MEDIUM);
    }
  }

  private getContentLengthInstructions(contentLength: ContentLength): string {
    switch (contentLength) {
      case ContentLength.SHORT:
        return `- Keep answers concise and to the point (1-2 sentences maximum)
- Focus on essential information only
- Use bullet points or brief phrases when appropriate
- Aim for quick, efficient study sessions`;

      case ContentLength.MEDIUM:
        return `- Provide moderate detail in answers (2-3 sentences)
- Include key points and some context
- Balance brevity with completeness
- Suitable for standard study sessions`;

      case ContentLength.LONG:
        return `- Provide comprehensive, detailed answers (3-5 sentences)
- Include context, examples, and additional explanations
- Cover multiple aspects of the topic
- Suitable for in-depth study and review`;

      default:
        return this.getContentLengthInstructions(ContentLength.MEDIUM);
    }
  }

  private buildQuizPrompt(
    content: string,
    count: number,
    customPrompt?: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): string {
    // Generate difficulty-specific instructions
    const difficultyInstructions = this.getDifficultyInstructions(difficultyLevel);

    // Generate content length-specific instructions
    const lengthInstructions = this.getContentLengthInstructions(contentLength);

    const basePrompt = `
Create ${count} diverse quiz questions from the following content. Include multiple choice, select all that apply, true/false, and short answer questions.

DIFFICULTY LEVEL: ${difficultyLevel.toUpperCase()}
${difficultyInstructions}

CONTENT LENGTH: ${contentLength.toUpperCase()}
${lengthInstructions}

General Guidelines:
- Create a mix of question types
- Ensure all questions are answerable from the content
- Make distractors plausible but clearly incorrect
- Include 3-4 options for multiple choice questions
- Provide clear explanations for answers

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "question_text": "Question here",
    "question_type": "multiple_choice|select_all|true_false|short_answer",
    "options": ["Option 1", "Option 2", "Option 3", "Option 4"] or null for short_answer and true_false,
    "correct_answers": ["Correct answer(s)"],
    "explanation": "Explanation of the correct answer",
    "difficulty_level": "${difficultyLevel}",
    "content_length": "${contentLength}"
  }
]

For true_false questions:
- Set "options": null
- Set "correct_answers": ["True"] or ["False"]

IMPORTANT:
- Ensure the response is valid JSON that can be parsed directly
- Complete ALL questions fully - do not truncate any content
- End the JSON array properly with a closing bracket ]
- Keep explanations concise but complete to avoid token limits`;

    return basePrompt;
  }

  private async callOpenRouter(prompt: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 16000, // Increased for longer responses
          top_p: 0.9
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://chewyai.com',
            'X-Title': 'ChewyAI Study Material Generator'
          },
          timeout: 60000 // 60 second timeout
        }
      );

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from AI service');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('AI service rate limit exceeded. Please try again later.');
        } else if (error.response?.status === 401) {
          throw new Error('AI service authentication failed');
        } else if (error.response?.status === 503) {
          const errorDetails = error.response?.data?.error?.metadata?.raw || '';
          if (errorDetails.includes('No instances available')) {
            throw new Error('AI model is currently overloaded. Please try again in a few minutes.');
          }
          throw new Error('AI service is temporarily unavailable. Please try again later.');
        } else if (error.response?.status === 404) {
          throw new Error('AI model not found. Please contact support.');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('AI service request timed out');
        }
      }
      throw new Error(`AI service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseFlashcardResponse(
    response: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged'>[] {
    try {
      // Clean response - remove any markdown formatting
      let cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();

      // Extract JSON array from response that may contain explanatory text
      // Look for the first occurrence of '[' and the last occurrence of ']'
      const firstBracket = cleanResponse.indexOf('[');
      const lastBracket = cleanResponse.lastIndexOf(']');

      if (firstBracket !== -1 && lastBracket !== -1 && firstBracket < lastBracket) {
        cleanResponse = cleanResponse.substring(firstBracket, lastBracket + 1);
      }

      // Check if response appears to be truncated
      let finalResponse = cleanResponse;
      if (!cleanResponse.endsWith(']') && !cleanResponse.endsWith('}]')) {
        console.warn('Flashcard response appears to be truncated, attempting to fix:', cleanResponse.slice(-200));

        // Try to salvage the response by finding the last complete flashcard
        const lastCompleteCardIndex = cleanResponse.lastIndexOf('},');
        if (lastCompleteCardIndex > 0) {
          // Truncate to the last complete flashcard and close the array
          finalResponse = cleanResponse.substring(0, lastCompleteCardIndex + 1) + '\n]';
          console.log('Attempting to salvage truncated flashcard response with', finalResponse.split('},').length, 'flashcards');
        } else {
          throw new Error('AI response appears to be truncated and cannot be salvaged. Try reducing the number of flashcards or content length.');
        }
      }

      const flashcards = JSON.parse(finalResponse);
      
      if (!Array.isArray(flashcards)) {
        throw new Error('Response is not an array');
      }

      return flashcards.map((card, index) => {
        if (!card.front || !card.back) {
          throw new Error(`Invalid flashcard at index ${index}: missing front or back`);
        }

        return {
          front: String(card.front).trim(),
          back: String(card.back).trim(),
          difficulty_level: this.validateDifficultyLevelEnum(card.difficulty_level, difficultyLevel),
          content_length: this.validateContentLengthEnum(card.content_length, contentLength),
          is_ai_generated: true,
          times_reviewed: 0,
          last_reviewed_at: undefined
        };
      });
    } catch (error) {
      console.error('Failed to parse flashcard response:', response.slice(0, 500) + '...');
      console.error('Full response length:', response.length);
      console.error('Parse error:', error instanceof Error ? error.message : 'Unknown error');
      throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseQuizResponse(
    response: string,
    difficultyLevel: DifficultyLevel = DifficultyLevel.MEDIUM,
    contentLength: ContentLength = ContentLength.MEDIUM
  ): Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[] {
    try {
      // Clean response - remove any markdown formatting
      let cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();

      // Extract JSON array from response that may contain explanatory text
      // Look for the first occurrence of '[' and the last occurrence of ']'
      const firstBracket = cleanResponse.indexOf('[');
      const lastBracket = cleanResponse.lastIndexOf(']');

      if (firstBracket !== -1 && lastBracket !== -1 && firstBracket < lastBracket) {
        cleanResponse = cleanResponse.substring(firstBracket, lastBracket + 1);
      }

      // Check if response appears to be truncated
      let finalResponse = cleanResponse;
      if (!cleanResponse.endsWith(']') && !cleanResponse.endsWith('}]')) {
        console.warn('Response appears to be truncated, attempting to fix:', cleanResponse.slice(-200));

        // Try to salvage the response by finding the last complete question
        const lastCompleteQuestionIndex = cleanResponse.lastIndexOf('},');
        if (lastCompleteQuestionIndex > 0) {
          // Truncate to the last complete question and close the array
          finalResponse = cleanResponse.substring(0, lastCompleteQuestionIndex + 1) + '\n]';
          console.log('Attempting to salvage truncated response with', finalResponse.split('},').length, 'questions');
        } else {
          throw new Error('AI response appears to be truncated and cannot be salvaged. Try reducing the number of questions or content length.');
        }
      }

      const questions = JSON.parse(finalResponse);
      
      if (!Array.isArray(questions)) {
        throw new Error('Response is not an array');
      }

      return questions.map((question, index) => {
        if (!question.question_text || !question.question_type || !question.correct_answers) {
          throw new Error(`Invalid question at index ${index}: missing required fields`);
        }

        const validTypes = ['multiple_choice', 'select_all', 'true_false', 'short_answer'];
        if (!validTypes.includes(question.question_type)) {
          throw new Error(`Invalid question type at index ${index}: ${question.question_type}`);
        }

        return {
          question_text: String(question.question_text).trim(),
          question_type: question.question_type,
          options: question.options ? question.options.map((opt: any) => String(opt).trim()) : undefined,
          correct_answers: Array.isArray(question.correct_answers)
            ? question.correct_answers.map((ans: any) => String(ans).trim())
            : [String(question.correct_answers).trim()],
          explanation: question.explanation ? String(question.explanation).trim() : undefined,
          difficulty_level: this.validateDifficultyLevelEnum(question.difficulty_level, difficultyLevel),
          content_length: this.validateContentLengthEnum(question.content_length, contentLength),
          is_ai_generated: true,
          times_attempted: 0,
          times_correct: 0
        };
      });
    } catch (error) {
      console.error('Failed to parse quiz response:', response);
      throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }



  private validateDifficultyLevelEnum(level: any, fallback: DifficultyLevel): DifficultyLevel {
    if (typeof level === 'string') {
      const normalizedLevel = level.toLowerCase();
      switch (normalizedLevel) {
        case 'easy': return DifficultyLevel.EASY;
        case 'medium': return DifficultyLevel.MEDIUM;
        case 'hard': return DifficultyLevel.HARD;
        case 'college': return DifficultyLevel.COLLEGE;
        case 'graduate': return DifficultyLevel.GRADUATE;
        case 'phd': return DifficultyLevel.PHD;
        default: return fallback;
      }
    }
    return fallback;
  }

  private validateContentLengthEnum(length: any, fallback: ContentLength): ContentLength {
    if (typeof length === 'string') {
      const normalizedLength = length.toLowerCase();
      switch (normalizedLength) {
        case 'short': return ContentLength.SHORT;
        case 'medium': return ContentLength.MEDIUM;
        case 'long': return ContentLength.LONG;
        default: return fallback;
      }
    }
    return fallback;
  }

  // Test AI service connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.callOpenRouter('Respond with "OK" if you can read this message.');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      console.error('AI service connection test failed:', error);
      return false;
    }
  }
}

export class AIError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'AIError';
  }
}

export const aiService = new AIService();
