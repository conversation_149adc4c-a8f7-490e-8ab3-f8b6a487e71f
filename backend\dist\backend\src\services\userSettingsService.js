"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userSettingsService = void 0;
const supabase_1 = require("../config/supabase");
class UserSettingsService {
    async getUserSettings(userId) {
        // First try to get existing settings
        const { data: existingSettings, error: selectError } = await supabase_1.supabase
            .from('user_settings')
            .select('*')
            .eq('user_id', userId)
            .single();
        if (selectError && selectError.code !== 'PGRST116') {
            // PGRST116 is "not found" error, other errors are actual problems
            throw new Error(`Failed to fetch user settings: ${selectError.message}`);
        }
        if (existingSettings) {
            return existingSettings;
        }
        // If no settings exist, create default settings
        return this.createDefaultSettings(userId);
    }
    async createDefaultSettings(userId) {
        const defaultSettings = {
            user_id: userId,
            skip_delete_confirmations: false,
            shuffle_flashcards: false
        };
        const { data, error } = await supabase_1.supabase
            .from('user_settings')
            .insert(defaultSettings)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create default user settings: ${error.message}`);
        }
        return data;
    }
    async updateUserSettings(userId, updates) {
        // Validate updates
        if (Object.keys(updates).length === 0) {
            throw new Error('No updates provided');
        }
        // Validate boolean fields
        if (updates.skip_delete_confirmations !== undefined && typeof updates.skip_delete_confirmations !== 'boolean') {
            throw new Error('skip_delete_confirmations must be a boolean');
        }
        if (updates.shuffle_flashcards !== undefined && typeof updates.shuffle_flashcards !== 'boolean') {
            throw new Error('shuffle_flashcards must be a boolean');
        }
        // First ensure settings exist
        await this.getUserSettings(userId);
        // Update settings
        const { data, error } = await supabase_1.supabase
            .from('user_settings')
            .update(updates)
            .eq('user_id', userId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update user settings: ${error.message}`);
        }
        return data;
    }
    async deleteUserSettings(userId) {
        const { error } = await supabase_1.supabase
            .from('user_settings')
            .delete()
            .eq('user_id', userId);
        if (error) {
            throw new Error(`Failed to delete user settings: ${error.message}`);
        }
    }
}
exports.userSettingsService = new UserSettingsService();
//# sourceMappingURL=userSettingsService.js.map