import{j as a}from"./index-5f534378.js";var e;(function(r){r.EASY="easy",r.MEDIUM="medium",r.HARD="hard",r.COLLEGE="college",r.GRADUATE="graduate",r.PHD="phd"})(e||(e={}));var n;(function(r){r.SHORT="short",r.MEDIUM="medium",r.LONG="long"})(n||(n={}));const m=r=>({[e.EASY]:"Easy",[e.MEDIUM]:"Medium",[e.HARD]:"Hard",[e.COLLEGE]:"College",[e.GRADUATE]:"Graduate",[e.PHD]:"PhD"})[r],y=r=>({[e.EASY]:1,[e.MEDIUM]:3,[e.HARD]:4,[e.COLLEGE]:5,[e.GRADUATE]:6,[e.PHD]:7})[r],h=r=>{switch(r){case 1:return e.EASY;case 2:return e.EASY;case 3:return e.MEDIUM;case 4:return e.HARD;case 5:return e.COLLEGE;case 6:return e.GRADUATE;case 7:return e.PHD;default:return e.MEDIUM}},u=[{value:e.EASY,label:"Easy",description:"Basic facts and definitions"},{value:e.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:e.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:e.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:e.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:e.PHD,label:"PhD",description:"Research-level expertise"}],g=r=>{switch(r){case e.EASY:return"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";case e.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case e.HARD:return"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";case e.COLLEGE:return"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";case e.GRADUATE:return"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";case e.PHD:return"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},p=r=>{switch(r){case e.EASY:return"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";case e.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case e.HARD:return"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";case e.COLLEGE:return"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";case e.GRADUATE:return"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";case e.PHD:return"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},x=({value:r,onChange:o,className:l="",disabled:d=!1,label:i="Difficulty Level"})=>a.jsxs("div",{className:`space-y-3 ${l}`,children:[a.jsx("label",{className:"block text-sm font-medium text-text-primary",children:i}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:u.map(t=>{const s=r===t.value,c=s?p(t.value):g(t.value);return a.jsxs("button",{type:"button",onClick:()=>!d&&o(t.value),disabled:d,className:`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${c}
                ${d?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${s?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:t.description,"aria-pressed":s,children:[a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"font-semibold",children:t.label}),a.jsx("div",{className:`text-xs mt-1 ${s?"text-white/90":"text-text-secondary"}`,children:t.description})]}),s&&a.jsx("div",{className:"absolute top-2 right-2",children:a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},t.value)})}),a.jsx("p",{className:"text-xs text-text-muted",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]});export{n as C,e as D,x as a,y as b,m as d,h as n};
