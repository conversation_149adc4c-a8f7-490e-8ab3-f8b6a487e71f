import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { getUserSettings, updateUserSettings, getUserPreferences, updateUserPreferences } from '../controllers/userSettingsController';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// GET /api/user/settings - Get user settings
router.get('/', getUserSettings);

// PATCH /api/user/settings - Update user settings
router.patch('/', updateUserSettings);

export default router;
