"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createStudySet = exports.updateStudyProgress = exports.getStudySetContent = exports.deleteStudySet = exports.updateStudySet = exports.getStudySet = exports.getStudySets = void 0;
const studySetService_1 = require("../services/studySetService");
const flashcardService_1 = require("../services/flashcardService");
const quizService_1 = require("../services/quizService");
const getStudySets = async (req, res) => {
    try {
        const userId = req.user.id;
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;
        const studySets = await studySetService_1.studySetService.getUserStudySets(userId, limit, offset);
        res.json({
            success: true,
            data: studySets
        });
    }
    catch (error) {
        console.error('Get study sets error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve study sets'
        });
    }
};
exports.getStudySets = getStudySets;
const getStudySet = async (req, res) => {
    try {
        const userId = req.user.id;
        const studySetId = req.params.id;
        const studySet = await studySetService_1.studySetService.getStudySetById(studySetId, userId);
        if (!studySet) {
            return res.status(404).json({
                success: false,
                error: 'Study set not found'
            });
        }
        res.json({
            success: true,
            data: studySet
        });
    }
    catch (error) {
        console.error('Get study set error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve study set'
        });
    }
};
exports.getStudySet = getStudySet;
const updateStudySet = async (req, res) => {
    try {
        const userId = req.user.id;
        const studySetId = req.params.id;
        const updates = req.body;
        // Only allow certain fields to be updated
        const allowedUpdates = ['name', 'custom_prompt'];
        const filteredUpdates = Object.keys(updates)
            .filter(key => allowedUpdates.includes(key))
            .reduce((obj, key) => {
            obj[key] = updates[key];
            return obj;
        }, {});
        if (Object.keys(filteredUpdates).length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No valid updates provided'
            });
        }
        const studySet = await studySetService_1.studySetService.updateStudySet(studySetId, userId, filteredUpdates);
        res.json({
            success: true,
            data: studySet
        });
    }
    catch (error) {
        console.error('Update study set error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update study set'
        });
    }
};
exports.updateStudySet = updateStudySet;
const deleteStudySet = async (req, res) => {
    try {
        const userId = req.user.id;
        const studySetId = req.params.id;
        await studySetService_1.studySetService.deleteStudySet(studySetId, userId);
        res.json({
            success: true,
            message: 'Study set deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete study set error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete study set'
        });
    }
};
exports.deleteStudySet = deleteStudySet;
const getStudySetContent = async (req, res) => {
    try {
        const userId = req.user.id;
        const studySetId = req.params.id;
        const studySet = await studySetService_1.studySetService.getStudySetById(studySetId, userId);
        if (!studySet) {
            return res.status(404).json({
                success: false,
                error: 'Study set not found'
            });
        }
        let content;
        if (studySet.type === 'flashcards') {
            content = await flashcardService_1.flashcardService.getFlashcardsByStudySet(studySetId, userId);
        }
        else if (studySet.type === 'quiz') {
            content = await quizService_1.quizService.getQuestionsByStudySet(studySetId, userId);
        }
        else {
            return res.status(400).json({
                success: false,
                error: 'Invalid study set type'
            });
        }
        res.json({
            success: true,
            data: {
                studySet,
                content
            }
        });
    }
    catch (error) {
        console.error('Get study set content error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve study set content'
        });
    }
};
exports.getStudySetContent = getStudySetContent;
const updateStudyProgress = async (req, res) => {
    try {
        const userId = req.user.id;
        const studySetId = req.params.id;
        // Update last studied timestamp
        await studySetService_1.studySetService.updateStudySet(studySetId, userId, {
            last_studied_at: new Date().toISOString()
        });
        res.json({
            success: true,
            message: 'Study progress updated'
        });
    }
    catch (error) {
        console.error('Update study progress error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update study progress'
        });
    }
};
exports.updateStudyProgress = updateStudyProgress;
const createStudySet = async (req, res) => {
    try {
        const userId = req.user.id;
        const { name, type } = req.body;
        // Validate input
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Study set name is required'
            });
        }
        if (!type || !['flashcards', 'quiz'].includes(type)) {
            return res.status(400).json({
                success: false,
                error: 'Valid study set type is required (flashcards or quiz)'
            });
        }
        const studySet = await studySetService_1.studySetService.createStudySet({
            user_id: userId,
            name: name.trim(),
            type,
            is_ai_generated: false
        });
        res.status(201).json({
            success: true,
            data: studySet,
            message: 'Study set created successfully'
        });
    }
    catch (error) {
        console.error('Create study set error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create study set'
        });
    }
};
exports.createStudySet = createStudySet;
//# sourceMappingURL=studySetController.js.map