{"version": 3, "file": "userSettingsService.js", "sourceRoot": "", "sources": ["../../../../src/services/userSettingsService.ts"], "names": [], "mappings": ";;;AAAA,iDAA8C;AAC9C,iDAAwD;AAyCxD,MAAM,mBAAmB;IACvB,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,qCAAqC;QACrC,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,mBAAQ;aAClE,IAAI,CAAC,eAAe,CAAC;aACrB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aACrB,MAAM,EAAE,CAAC;QAEZ,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACnD,kEAAkE;YAClE,MAAM,IAAI,KAAK,CAAC,kCAAkC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,gDAAgD;QAChD,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE,MAAM;YACf,yBAAyB,EAAE,KAAK;YAChC,kBAAkB,EAAE,KAAK;SAC1B,CAAC;QAEF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aACnC,IAAI,CAAC,eAAe,CAAC;aACrB,MAAM,CAAC,eAAe,CAAC;aACvB,MAAM,EAAE;aACR,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,OAA2B;QAClE,mBAAmB;QACnB,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,yBAAyB,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,yBAAyB,KAAK,SAAS,EAAE,CAAC;YAC9G,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,OAAO,CAAC,kBAAkB,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAChG,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEnC,kBAAkB;QAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aACnC,IAAI,CAAC,eAAe,CAAC;aACrB,MAAM,CAAC,OAAO,CAAC;aACf,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aACrB,MAAM,EAAE;aACR,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aAC7B,IAAI,CAAC,eAAe,CAAC;aACrB,MAAM,EAAE;aACR,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEzB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;CACF;AAEY,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAE7D,2BAA2B;AAC3B,MAAM,sBAAsB;IAC1B,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,wCAAwC;QACxC,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,mBAAQ;aACrE,IAAI,CAAC,kBAAkB,CAAC;aACxB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aACrB,MAAM,EAAE,CAAC;QAEZ,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACnD,kEAAkE;YAClE,MAAM,IAAI,KAAK,CAAC,qCAAqC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,sDAAsD;QACtD,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,MAAc;QAC3C,MAAM,kBAAkB,GAAG;YACzB,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,MAAe;YACtB,QAAQ,EAAE,IAAI;YACd,eAAe,EAAE,IAAI;YACrB,SAAS,EAAE,IAAI;YACf,kBAAkB,EAAE,YAAqB;YACzC,gBAAgB,EAAE,EAAE;YACpB,gBAAgB,EAAE,uBAAe,CAAC,MAAM;SACzC,CAAC;QAEF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aACnC,IAAI,CAAC,kBAAkB,CAAC;aACxB,MAAM,CAAC,kBAAkB,CAAC;aAC1B,MAAM,EAAE;aACR,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,8CAA8C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,OAA8B;QACxE,mBAAmB;QACnB,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,iBAAiB;QACjB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,8BAA8B;QAC9B,IAAI,OAAO,CAAC,kBAAkB,KAAK,SAAS,IAAI,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7G,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,IAAI,CAAC,OAAO,OAAO,CAAC,gBAAgB,KAAK,QAAQ,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC;YAC9H,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACjH,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YAC1F,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEtC,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aACnC,IAAI,CAAC,kBAAkB,CAAC;aACxB,MAAM,CAAC,OAAO,CAAC;aACf,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aACrB,MAAM,EAAE;aACR,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aAC7B,IAAI,CAAC,kBAAkB,CAAC;aACxB,MAAM,EAAE;aACR,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEzB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;CACF;AAEY,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC"}