import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HiCreditCard, 
  HiCurrencyDollar, 
  Hi<PERSON>hartBar, 
  HiC<PERSON>,
  HiRefresh,
  HiExclamationCircle
} from 'react-icons/hi';
import { useCreditStore } from '../stores/creditStore';
import { useAuthStore } from '../stores/authStore';
import { Button } from '../components/common/Button';
import { CreditBalance } from '../components/credits/CreditBalance';
import { CreditHistory } from '../components/credits/CreditHistory';
import { CreditPurchase } from '../components/credits/CreditPurchase';
import { CreditUsageChart } from '../components/credits/CreditUsageChart';

interface TabSection {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const creditTabs: TabSection[] = [
  {
    id: 'overview',
    label: 'Overview',
    icon: HiChartBar,
    description: 'Credit balance and usage summary'
  },
  {
    id: 'history',
    label: 'Transaction History',
    icon: HiClock,
    description: 'Detailed credit transaction log'
  },
  {
    id: 'purchase',
    label: 'Buy Credits',
    icon: HiCurrencyDollar,
    description: 'Purchase additional credits'
  }
];

export const CreditsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const { user } = useAuthStore();
  const { 
    balance, 
    transactions, 
    operationCosts, 
    stats, 
    isLoading, 
    error,
    fetchBalance,
    fetchTransactions,
    fetchOperationCosts,
    fetchStats,
    clearError
  } = useCreditStore();

  useEffect(() => {
    // Fetch initial data when component mounts
    fetchBalance();
    fetchTransactions();
    fetchOperationCosts();
    fetchStats();
  }, [fetchBalance, fetchTransactions, fetchOperationCosts, fetchStats]);

  const handleRefresh = async () => {
    clearError();
    await Promise.all([
      fetchBalance(),
      fetchTransactions(),
      fetchOperationCosts(),
      fetchStats()
    ]);
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Credit Balance Section */}
      <CreditBalance 
        balance={balance}
        userTier={user?.subscription_tier || 'Free'}
        isLoading={isLoading}
      />

      {/* Usage Statistics */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CreditUsageChart 
            stats={stats}
            operationCosts={operationCosts}
          />
          
          <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
            <h3 className="text-lg font-semibold text-white mb-4">Usage Breakdown</h3>
            <div className="space-y-3">
              {stats?.usageByOperation ? Object.entries(stats.usageByOperation).map(([operation, credits]) => (
                <div key={operation} className="flex justify-between items-center">
                  <span className="text-gray-300 capitalize">
                    {operation.replace(/_/g, ' ')}
                  </span>
                  <span className="text-white font-medium">{credits} credits</span>
                </div>
              )) : (
                <div className="text-gray-400 text-center py-4">
                  No usage data available
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Operation Costs */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h3 className="text-lg font-semibold text-white mb-4">Credit Costs</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {operationCosts?.length > 0 ? operationCosts.map((cost) => (
            <div 
              key={cost.operation_type}
              className="bg-background-tertiary rounded-lg p-4 border border-border-secondary"
            >
              <h4 className="font-medium text-white capitalize mb-2">
                {cost.operation_type.replace(/_/g, ' ')}
              </h4>
              <div className="flex items-center space-x-2">
                <HiCreditCard className="w-4 h-4 text-primary-400" />
                <span className="text-primary-400 font-semibold">
                  {cost.credits_required} credits
                </span>
              </div>
            </div>
          )) : (
            <div className="col-span-full text-gray-400 text-center py-8">
              No operation cost data available
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderHistoryTab = () => (
    <CreditHistory 
      transactions={transactions}
      isLoading={isLoading}
      onLoadMore={() => fetchTransactions(50, transactions.length)}
    />
  );

  const renderPurchaseTab = () => (
    <CreditPurchase 
      currentBalance={balance}
      userTier={user?.subscription_tier || 'Free'}
      onPurchaseComplete={handleRefresh}
    />
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'history':
        return renderHistoryTab();
      case 'purchase':
        return renderPurchaseTab();
      default:
        return renderOverviewTab();
    }
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Credits</h1>
            <p className="text-gray-400">Manage your AI generation credits</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleRefresh}
              variant="secondary"
              disabled={isLoading}
            >
              <HiRefresh className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
            <Button
              onClick={clearError}
              variant="secondary"
              size="sm"
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Tab Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
              <nav className="space-y-2">
                {creditTabs.map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;
                  
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${isActive 
                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30' 
                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block">{tab.label}</span>
                        <span className="text-xs text-gray-500 block truncate">
                          {tab.description}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
