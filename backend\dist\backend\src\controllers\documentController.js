"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchDocuments = exports.deleteDocument = exports.getDocument = exports.getDocuments = exports.uploadDocument = void 0;
const multer_1 = __importDefault(require("multer"));
const documentService_1 = require("../services/documentService");
const documentDbService_1 = require("../services/documentDbService");
// Configure multer for file uploads
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error('Unsupported file type'), false);
        }
    }
});
const processor = new documentService_1.DocumentProcessor();
const fileUploadService = new documentService_1.FileUploadService();
exports.uploadDocument = [
    upload.single('document'),
    (error, _req, res, next) => {
        if (error) {
            if (error.code === 'LIMIT_FILE_SIZE') {
                return res.status(413).json({
                    success: false,
                    error: 'File too large. Maximum size is 10MB.'
                });
            }
            if (error.message === 'Unsupported file type') {
                return res.status(415).json({
                    success: false,
                    error: 'Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.'
                });
            }
            return res.status(400).json({
                success: false,
                error: error.message
            });
        }
        next();
    },
    async (req, res) => {
        try {
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    error: 'No file uploaded'
                });
            }
            const userId = req.user.id;
            const file = req.file;
            // Process file and extract text
            let extractedText;
            try {
                extractedText = await processor.processFile(file);
            }
            catch (processingError) {
                console.error('File processing error:', processingError);
                return res.status(415).json({
                    success: false,
                    error: 'Unsupported file type or corrupted file'
                });
            }
            // Upload original file to Supabase Storage (simplified path for testing)
            const fileName = `${Date.now()}_${file.originalname}`;
            let storagePath;
            try {
                storagePath = await fileUploadService.uploadFile(fileName, file.buffer, file.mimetype);
            }
            catch (storageError) {
                console.error('Storage upload error:', storageError);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to store file'
                });
            }
            // Insert document metadata and content into database
            try {
                const documentData = {
                    user_id: userId,
                    filename: file.originalname,
                    file_type: getFileTypeFromMimetype(file.mimetype),
                    file_size: file.size,
                    content_text: extractedText,
                    supabase_storage_path: storagePath,
                    is_processed: true,
                    processing_error: undefined
                };
                const document = await documentDbService_1.documentDbService.createDocument(documentData);
                return res.status(201).json({
                    success: true,
                    data: document,
                    message: 'Document uploaded and processed successfully'
                });
            }
            catch (dbError) {
                console.error('Database error:', dbError);
                // Cleanup - delete uploaded file if database insert fails
                try {
                    await fileUploadService.deleteFile(storagePath);
                }
                catch (cleanupError) {
                    console.error('Failed to cleanup file after database error:', cleanupError);
                }
                return res.status(500).json({
                    success: false,
                    error: 'Failed to save document metadata'
                });
            }
        }
        catch (error) {
            console.error('Unexpected error in uploadDocument:', error);
            return res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
];
const getDocuments = async (req, res) => {
    try {
        const userId = req.user.id;
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;
        const documents = await documentDbService_1.documentDbService.getUserDocuments(userId, limit, offset);
        res.json({
            success: true,
            data: documents
        });
    }
    catch (error) {
        console.error('Get documents error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve documents'
        });
    }
};
exports.getDocuments = getDocuments;
const getDocument = async (req, res) => {
    try {
        const userId = req.user.id;
        const documentId = req.params.id;
        const document = await documentDbService_1.documentDbService.getDocumentById(documentId, userId);
        if (!document) {
            return res.status(404).json({
                success: false,
                error: 'Document not found'
            });
        }
        res.json({
            success: true,
            data: document
        });
    }
    catch (error) {
        console.error('Get document error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve document'
        });
    }
};
exports.getDocument = getDocument;
const deleteDocument = async (req, res) => {
    try {
        const userId = req.user.id;
        const documentId = req.params.id;
        // Get document to find storage path
        const document = await documentDbService_1.documentDbService.getDocumentById(documentId, userId);
        if (!document) {
            return res.status(404).json({
                success: false,
                error: 'Document not found'
            });
        }
        // Delete from database first
        await documentDbService_1.documentDbService.deleteDocument(documentId, userId);
        // Delete from storage (don't fail if this fails)
        try {
            await fileUploadService.deleteFile(document.supabase_storage_path);
        }
        catch (storageError) {
            console.error('Storage deletion error:', storageError);
            // Continue - database deletion succeeded
        }
        res.json({
            success: true,
            message: 'Document deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete document error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete document'
        });
    }
};
exports.deleteDocument = deleteDocument;
const searchDocuments = async (req, res) => {
    try {
        const userId = req.user.id;
        const query = req.query.q;
        if (!query || query.trim().length < 2) {
            return res.status(400).json({
                success: false,
                error: 'Search query must be at least 2 characters'
            });
        }
        const documents = await documentDbService_1.documentDbService.searchDocuments(userId, query.trim());
        res.json({
            success: true,
            data: documents
        });
    }
    catch (error) {
        console.error('Search documents error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to search documents'
        });
    }
};
exports.searchDocuments = searchDocuments;
function getFileTypeFromMimetype(mimetype) {
    const typeMap = {
        'application/pdf': 'pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'text/plain': 'txt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx'
    };
    return typeMap[mimetype] || 'txt';
}
//# sourceMappingURL=documentController.js.map