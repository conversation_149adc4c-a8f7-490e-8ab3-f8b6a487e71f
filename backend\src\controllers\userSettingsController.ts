import { Request, Response } from 'express';
import { userSettingsService, UserSettingsUpdate } from '../services/userSettingsService';
import { DifficultyLevel } from '../../../shared/types';

export const getUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const settings = await userSettingsService.getUserSettings(userId);

    res.json({
      success: true,
      data: settings
    });
  } catch (error: any) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch user settings'
    });
  }
};

export const updateUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const updates: UserSettingsUpdate = req.body;

    // Validate request body
    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body'
      });
    }

    // Validate allowed fields
    const allowedFields = ['skip_delete_confirmations', 'shuffle_flashcards'];
    const providedFields = Object.keys(updates);
    const invalidFields = providedFields.filter(field => !allowedFields.includes(field));

    if (invalidFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid fields: ${invalidFields.join(', ')}`
      });
    }

    if (providedFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid fields provided for update'
      });
    }

    const updatedSettings = await userSettingsService.updateUserSettings(userId, updates);

    res.json({
      success: true,
      data: updatedSettings
    });
  } catch (error: any) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user settings'
    });
  }
};

// User Preferences interface
interface UserPreferences {
  theme: 'dark' | 'light';
  language: string;
  studyReminders: boolean;
  autoSave: boolean;
  defaultStudyMode: 'flashcards' | 'quiz';
  sessionDuration: number;
  difficultyLevel: DifficultyLevel;
}

export const getUserPreferences = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const settings = await userSettingsService.getUserSettings(userId);

    // Extract preferences from settings or provide defaults
    const preferences: UserPreferences = {
      theme: 'dark',
      language: 'en',
      studyReminders: true,
      autoSave: true,
      defaultStudyMode: 'flashcards',
      sessionDuration: 30,
      difficultyLevel: DifficultyLevel.MEDIUM
    };

    res.json({
      success: true,
      data: preferences
    });
  } catch (error: any) {
    console.error('Get user preferences error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch user preferences'
    });
  }
};

export const updateUserPreferences = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const preferences: Partial<UserPreferences> = req.body;

    // Validate request body
    if (!preferences || typeof preferences !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body'
      });
    }

    // Validate allowed fields
    const allowedFields = ['theme', 'language', 'studyReminders', 'autoSave', 'defaultStudyMode', 'sessionDuration', 'difficultyLevel'];
    const providedFields = Object.keys(preferences);
    const invalidFields = providedFields.filter(field => !allowedFields.includes(field));

    if (invalidFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid fields: ${invalidFields.join(', ')}`
      });
    }

    if (providedFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid fields provided for update'
      });
    }

    // Validate difficulty level if provided
    if (preferences.difficultyLevel && !Object.values(DifficultyLevel).includes(preferences.difficultyLevel)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid difficulty level'
      });
    }

    // For now, just return success with the updated preferences
    // In a real implementation, you would save these to the database
    const updatedPreferences: UserPreferences = {
      theme: preferences.theme || 'dark',
      language: preferences.language || 'en',
      studyReminders: preferences.studyReminders !== undefined ? preferences.studyReminders : true,
      autoSave: preferences.autoSave !== undefined ? preferences.autoSave : true,
      defaultStudyMode: preferences.defaultStudyMode || 'flashcards',
      sessionDuration: preferences.sessionDuration || 30,
      difficultyLevel: preferences.difficultyLevel || DifficultyLevel.MEDIUM
    };

    res.json({
      success: true,
      data: updatedPreferences
    });
  } catch (error: any) {
    console.error('Update user preferences error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user preferences'
    });
  }
};
