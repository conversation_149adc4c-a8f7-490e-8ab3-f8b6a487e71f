const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testDocumentUpload() {
  try {
    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpass123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);
    const token = loginData.token;
    console.log('✅ Login successful');

    // Now test document upload
    console.log('📄 Uploading test document...');
    const form = new FormData();
    form.append('document', fs.createReadStream('test-document.txt'));

    const uploadResponse = await fetch('http://localhost:3001/api/documents/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders()
      },
      body: form
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadData = await uploadResponse.json();
    console.log('✅ Document uploaded successfully:', uploadData);

    // Test getting documents
    console.log('📋 Getting user documents...');
    const getResponse = await fetch('http://localhost:3001/api/documents', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!getResponse.ok) {
      throw new Error(`Get documents failed: ${getResponse.status}`);
    }

    const documents = await getResponse.json();
    console.log('✅ Documents retrieved:', documents);

    // Test search
    console.log('🔍 Searching documents...');
    const searchResponse = await fetch('http://localhost:3001/api/documents/search?q=test', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!searchResponse.ok) {
      throw new Error(`Search failed: ${searchResponse.status}`);
    }

    const searchResults = await searchResponse.json();
    console.log('✅ Search results:', searchResults);

    console.log('\n🎉 All tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testDocumentUpload();
