import { Request, Response } from 'express';
export declare const getUserSettings: (req: Request, res: Response) => Promise<void>;
export declare const updateUserSettings: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getUserPreferences: (_req: Request, res: Response) => Promise<void>;
export declare const updateUserPreferences: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=userSettingsController.d.ts.map