# Agent Command: Start Development Workflow

## Status
You have been successfully primed with the ChewyAI project context, including all documentation, progress, and workflow standards. Your next step is to begin implementation.

## Step 1: Confirm Next Phase & Create Feature Branch
**Action:** Execute the following steps precisely.

1.  **State the Next Phase:** Based on the `chewyai_spec/` folder contents you reviewed, explicitly state the number and title of the **next logical task file** you will create. For example: `Next Task: 11_frontend_study_interfaces.md`.

2.  **Execute Git Workflow:** Create the feature branch for this new phase. **You must construct the branch name dynamically based on the phase you identified.**
    ```bash
    # Ensure you are on the staging branch and it's up-to-date
    git checkout staging
    git pull origin staging

    # Create and switch to the new feature branch (e.g., feature/phase-11-study-interfaces)
    git checkout -b feature/phase-[NUMBER]-[description-from-identified-phase]
    
    # Push the new branch to remote to establish tracking
    git push --set-upstream origin feature/phase-[NUMBER]-[description-from-identified-phase]
    ```

## Step 2: Update Project Progress Log
**Action:** Before writing any implementation code, create or update the project's progress log. This provides a clear marker of what work is currently in progress.

- **File:** `PROGRESS.md` (in the root directory)
- **Content:** Add a new entry to this file with the current date and the phase you are about to start.
    - **Example Format:**
      ```markdown
      ## [YYYY-MM-DD] - In Progress: Phase [Number] - [Phase Title]
      
      * **Objective:** [Briefly describe the goal of this phase]
      * **Branch:** `feature/phase-[NUMBER]-[description]`
      * **Key Files:**
          * `chewyai_spec/[NUMBER]_[title].md`
          * `frontend/src/components/study/...`
      ```

## Step 3: Begin Implementation
**Action:** Proceed with the primary task of this session.

1.  **Create the Task File:** Create the new numbered `.md` specification file inside the `chewyai_spec/` directory.
2.  **Define the Work:** Populate this file with the complete plan for the phase, including goals, detailed sub-tasks, file paths, and code generation instructions, following the established format of previous task files.
3.  **Await Confirmation:** After you have generated the complete content for the new task specification file, stop and await my confirmation to proceed with the code implementation outlined within it.

**Execute Now.**