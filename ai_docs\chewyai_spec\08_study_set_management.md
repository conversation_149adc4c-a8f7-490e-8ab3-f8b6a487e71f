# Phase 8: Study Set Management Backend
**Priority**: HIGH - Core study functionality
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database), Phase 3 (Authentication), Phase 7 (AI Integration)
**Estimated Time**: 3-4 hours

## Overview
Complete study set CRUD operations, flashcard and quiz question management, and study progress tracking.

## Tasks

### 8.1 Flashcard Management Service
**File**: `backend/src/services/flashcardService.ts`

```typescript
import { supabase } from './supabaseService';
import { Flashcard, FlashcardData } from '../../../shared/types';

export class FlashcardService {
  async getFlashcardsByStudySet(studySetId: string, userId: string): Promise<Flashcard[]> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .select('*')
      .eq('study_set_id', studySetId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get flashcards: ${error.message}`);
    }

    return data || [];
  }

  async createFlashcard(studySetId: string, userId: string, flashcardData: FlashcardData): Promise<Flashcard> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .insert({
        study_set_id: studySetId,
        front: flashcardData.front,
        back: flashcardData.back,
        difficulty_level: flashcardData.difficulty_level || 3,
        is_ai_generated: flashcardData.is_ai_generated || false
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create flashcard: ${error.message}`);
    }

    return data;
  }

  async updateFlashcard(flashcardId: string, userId: string, updates: Partial<FlashcardData>): Promise<Flashcard> {
    // Verify user owns the flashcard through study set
    const { data: flashcard, error: flashcardError } = await supabase
      .from('flashcards')
      .select('study_set_id')
      .eq('id', flashcardId)
      .single();

    if (flashcardError || !flashcard) {
      throw new Error('Flashcard not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', flashcard.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .update(updates)
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update flashcard: ${error.message}`);
    }

    return data;
  }

  async deleteFlashcard(flashcardId: string, userId: string): Promise<void> {
    // Verify user owns the flashcard through study set
    const { data: flashcard, error: flashcardError } = await supabase
      .from('flashcards')
      .select('study_set_id')
      .eq('id', flashcardId)
      .single();

    if (flashcardError || !flashcard) {
      throw new Error('Flashcard not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', flashcard.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { error } = await supabase
      .from('flashcards')
      .delete()
      .eq('id', flashcardId);

    if (error) {
      throw new Error(`Failed to delete flashcard: ${error.message}`);
    }
  }

  async updateFlashcardProgress(flashcardId: string, userId: string): Promise<Flashcard> {
    const { data, error } = await supabase
      .from('flashcards')
      .update({
        times_reviewed: supabase.sql`times_reviewed + 1`,
        last_reviewed_at: new Date().toISOString()
      })
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update flashcard progress: ${error.message}`);
    }

    return data;
  }

  async toggleFlashcardFlag(flashcardId: string, userId: string): Promise<Flashcard> {
    // Get current flag status
    const { data: current, error: getCurrentError } = await supabase
      .from('flashcards')
      .select('is_flagged, study_set_id')
      .eq('id', flashcardId)
      .single();

    if (getCurrentError || !current) {
      throw new Error('Flashcard not found');
    }

    // Verify ownership
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', current.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .update({ is_flagged: !current.is_flagged })
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to toggle flashcard flag: ${error.message}`);
    }

    return data;
  }
}

export const flashcardService = new FlashcardService();
```

### 8.2 Quiz Question Management Service
**File**: `backend/src/services/quizService.ts`

```typescript
import { supabase } from './supabaseService';
import { QuizQuestion, QuizQuestionData } from '../../../shared/types';

export class QuizService {
  async getQuestionsByStudySet(studySetId: string, userId: string): Promise<QuizQuestion[]> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('quiz_questions')
      .select('*')
      .eq('study_set_id', studySetId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get quiz questions: ${error.message}`);
    }

    return data || [];
  }

  async createQuizQuestion(studySetId: string, userId: string, questionData: QuizQuestionData): Promise<QuizQuestion> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('quiz_questions')
      .insert({
        study_set_id: studySetId,
        question_text: questionData.question_text,
        question_type: questionData.question_type,
        options: questionData.options,
        correct_answers: questionData.correct_answers,
        explanation: questionData.explanation,
        difficulty_level: questionData.difficulty_level || 3,
        is_ai_generated: questionData.is_ai_generated || false
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create quiz question: ${error.message}`);
    }

    return data;
  }

  async updateQuizQuestion(questionId: string, userId: string, updates: Partial<QuizQuestionData>): Promise<QuizQuestion> {
    // Verify user owns the question through study set
    const { data: question, error: questionError } = await supabase
      .from('quiz_questions')
      .select('study_set_id')
      .eq('id', questionId)
      .single();

    if (questionError || !question) {
      throw new Error('Quiz question not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', question.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { data, error } = await supabase
      .from('quiz_questions')
      .update(updates)
      .eq('id', questionId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update quiz question: ${error.message}`);
    }

    return data;
  }

  async deleteQuizQuestion(questionId: string, userId: string): Promise<void> {
    // Verify user owns the question through study set
    const { data: question, error: questionError } = await supabase
      .from('quiz_questions')
      .select('study_set_id')
      .eq('id', questionId)
      .single();

    if (questionError || !question) {
      throw new Error('Quiz question not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', question.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { error } = await supabase
      .from('quiz_questions')
      .delete()
      .eq('id', questionId);

    if (error) {
      throw new Error(`Failed to delete quiz question: ${error.message}`);
    }
  }

  async recordQuizAttempt(questionId: string, userId: string, isCorrect: boolean): Promise<QuizQuestion> {
    const updateData = {
      times_attempted: supabase.sql`times_attempted + 1`,
      ...(isCorrect && { times_correct: supabase.sql`times_correct + 1` })
    };

    const { data, error } = await supabase
      .from('quiz_questions')
      .update(updateData)
      .eq('id', questionId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to record quiz attempt: ${error.message}`);
    }

    return data;
  }

  async getQuizStatistics(studySetId: string, userId: string): Promise<{
    totalQuestions: number;
    totalAttempts: number;
    correctAnswers: number;
    averageScore: number;
    questionStats: Array<{
      id: string;
      question_text: string;
      times_attempted: number;
      times_correct: number;
      success_rate: number;
    }>;
  }> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data: questions, error } = await supabase
      .from('quiz_questions')
      .select('id, question_text, times_attempted, times_correct')
      .eq('study_set_id', studySetId);

    if (error) {
      throw new Error(`Failed to get quiz statistics: ${error.message}`);
    }

    const totalQuestions = questions?.length || 0;
    const totalAttempts = questions?.reduce((sum, q) => sum + q.times_attempted, 0) || 0;
    const correctAnswers = questions?.reduce((sum, q) => sum + q.times_correct, 0) || 0;
    const averageScore = totalAttempts > 0 ? (correctAnswers / totalAttempts) * 100 : 0;

    const questionStats = questions?.map(q => ({
      id: q.id,
      question_text: q.question_text,
      times_attempted: q.times_attempted,
      times_correct: q.times_correct,
      success_rate: q.times_attempted > 0 ? (q.times_correct / q.times_attempted) * 100 : 0
    })) || [];

    return {
      totalQuestions,
      totalAttempts,
      correctAnswers,
      averageScore,
      questionStats
    };
  }
}

export const quizService = new QuizService();
```

### 8.3 Study Set Controller
**File**: `backend/src/controllers/studySetController.ts`

```typescript
import { Request, Response } from 'express';
import { studySetService } from '../services/studySetService';
import { flashcardService } from '../services/flashcardService';
import { quizService } from '../services/quizService';

export const getStudySets = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const studySets = await studySetService.getUserStudySets(userId, limit, offset);

    res.json({
      success: true,
      data: studySets
    });
  } catch (error) {
    console.error('Get study sets error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve study sets'
    });
  }
};

export const getStudySet = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.id;

    const studySet = await studySetService.getStudySetById(studySetId, userId);

    if (!studySet) {
      return res.status(404).json({
        success: false,
        error: 'Study set not found'
      });
    }

    res.json({
      success: true,
      data: studySet
    });
  } catch (error) {
    console.error('Get study set error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve study set'
    });
  }
};

export const updateStudySet = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.id;
    const updates = req.body;

    // Only allow certain fields to be updated
    const allowedUpdates = ['name', 'custom_prompt'];
    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid updates provided'
      });
    }

    const studySet = await studySetService.updateStudySet(studySetId, userId, filteredUpdates);

    res.json({
      success: true,
      data: studySet
    });
  } catch (error) {
    console.error('Update study set error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update study set'
    });
  }
};

export const deleteStudySet = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.id;

    await studySetService.deleteStudySet(studySetId, userId);

    res.json({
      success: true,
      message: 'Study set deleted successfully'
    });
  } catch (error) {
    console.error('Delete study set error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete study set'
    });
  }
};

export const getStudySetContent = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.id;

    const studySet = await studySetService.getStudySetById(studySetId, userId);

    if (!studySet) {
      return res.status(404).json({
        success: false,
        error: 'Study set not found'
      });
    }

    let content;
    if (studySet.type === 'flashcards') {
      content = await flashcardService.getFlashcardsByStudySet(studySetId, userId);
    } else if (studySet.type === 'quiz') {
      content = await quizService.getQuestionsByStudySet(studySetId, userId);
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid study set type'
      });
    }

    res.json({
      success: true,
      data: {
        studySet,
        content
      }
    });
  } catch (error) {
    console.error('Get study set content error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve study set content'
    });
  }
};

export const updateStudyProgress = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.id;

    // Update last studied timestamp
    await studySetService.updateStudySet(studySetId, userId, {
      last_studied_at: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Study progress updated'
    });
  } catch (error) {
    console.error('Update study progress error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update study progress'
    });
  }
};
```

### 8.4 Study Set Routes
**File**: `backend/src/routes/studySets.ts`

```typescript
import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getStudySets,
  getStudySet,
  updateStudySet,
  deleteStudySet,
  getStudySetContent,
  updateStudyProgress
} from '../controllers/studySetController';

const router = Router();

// All study set routes require authentication
router.use(authenticateToken);

// Study set CRUD operations
router.get('/', getStudySets);
router.get('/:id', getStudySet);
router.put('/:id', updateStudySet);
router.delete('/:id', deleteStudySet);

// Study set content and progress
router.get('/:id/content', getStudySetContent);
router.post('/:id/study', updateStudyProgress);

export default router;
```

### 8.5 Integration with Main App
**File**: `backend/src/app.ts` (additions)

```typescript
// Add to existing app.ts
import studySetRoutes from './routes/studySets';

// Add after other route registrations
app.use('/api/study-sets', studySetRoutes);
```

## Acceptance Criteria
- [ ] Study set CRUD operations work correctly
- [ ] Flashcard management with progress tracking
- [ ] Quiz question management with attempt recording
- [ ] Study set content retrieval includes all items
- [ ] Progress tracking updates timestamps
- [ ] Statistics calculation for quiz performance
- [ ] Proper ownership validation for all operations
- [ ] Error handling for invalid requests
- [ ] Database triggers maintain item counts
- [ ] All operations respect RLS policies

## Next Phase Dependencies
- Phase 9 (Frontend Study Components) requires these API endpoints
- Phase 10 (Study Interfaces) requires content retrieval functionality
- Phase 11 (Progress Tracking) requires statistics endpoints
