import{c as A,j as e,n as b,E as v,i as k,o as L,m as T,r as N,F as _,B as w,A as E,G as U,J as $,K as I,q as H,L as F,t as z,b as O,C as M,D as R,s as q,M as V}from"./index-5f534378.js";const D=A((r,l)=>({balance:0,transactions:[],operationCosts:[],stats:null,isLoading:!1,error:null,fetchBalance:async()=>{r({isLoading:!0,error:null});try{const i=await fetch("/api/credits/balance",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!i.ok)throw new Error("Failed to fetch credit balance");const a=await i.json();if(a.success)r({balance:a.data.credits,isLoading:!1});else throw new Error(a.error||"Failed to fetch balance")}catch(i){r({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchTransactions:async(i=50,a=0)=>{r({isLoading:!0,error:null});try{const d=await fetch(`/api/credits/history?limit=${i}&offset=${a}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!d.ok)throw new Error("Failed to fetch credit history");const h=await d.json();if(h.success)r({transactions:a===0?h.data:[...l().transactions,...h.data],isLoading:!1});else throw new Error(h.error||"Failed to fetch transactions")}catch(d){r({error:d instanceof Error?d.message:"Unknown error",isLoading:!1})}},fetchOperationCosts:async()=>{r({isLoading:!0,error:null});try{const i=await fetch("/api/credits/pricing",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!i.ok)throw new Error("Failed to fetch operation costs");const a=await i.json();if(a.success)r({operationCosts:a.data,isLoading:!1});else throw new Error(a.error||"Failed to fetch operation costs")}catch(i){r({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchStats:async(i=30)=>{r({isLoading:!0,error:null});try{const a=await fetch(`/api/credits/stats?days=${i}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!a.ok)throw new Error("Failed to fetch credit stats");const d=await a.json();if(d.success)r({stats:d.data,isLoading:!1});else throw new Error(d.error||"Failed to fetch stats")}catch(a){r({error:a instanceof Error?a.message:"Unknown error",isLoading:!1})}},purchaseCredits:async i=>{r({isLoading:!0,error:null});try{const a=await fetch("/api/credits/purchase",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`},body:JSON.stringify({amount:i})});if(!a.ok)throw new Error("Failed to initiate credit purchase");const d=await a.json();if(d.success)return await l().fetchBalance(),await l().fetchTransactions(),r({isLoading:!1}),{success:!0};throw new Error(d.error||"Failed to purchase credits")}catch(a){const d=a instanceof Error?a.message:"Unknown error";return r({error:d,isLoading:!1}),{success:!1,error:d}}},clearError:()=>r({error:null})})),Y=({balance:r,userTier:l,isLoading:i})=>{const a=m=>{switch(m.toLowerCase()){case"pro":return"text-purple-400";case"basic":return"text-blue-400";default:return"text-gray-400"}},d=m=>{switch(m.toLowerCase()){case"pro":return e.jsx(T,{className:"w-5 h-5"});case"basic":return e.jsx(L,{className:"w-5 h-5"});default:return e.jsx(v,{className:"w-5 h-5"})}},o=(m=>m>=100?{color:"text-green-400",status:"Excellent"}:m>=50?{color:"text-yellow-400",status:"Good"}:m>=10?{color:"text-orange-400",status:"Low"}:{color:"text-red-400",status:"Critical"})(r);return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(v,{className:"w-6 h-6 text-primary-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Credit Balance"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Available for AI generation"})]})]}),e.jsxs("div",{className:`flex items-center space-x-2 ${a(l)}`,children:[d(l),e.jsxs("span",{className:"font-medium",children:[l," Plan"]})]})]}),e.jsxs("div",{className:"flex items-end space-x-4",children:[e.jsx("div",{children:i?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-12 w-32 bg-gray-600 rounded"})}):e.jsxs(b.div,{initial:{scale:.8},animate:{scale:1},transition:{duration:.3,delay:.2},children:[e.jsx("span",{className:"text-4xl font-bold text-white",children:r.toLocaleString()}),e.jsx("span",{className:"text-xl text-gray-400 ml-2",children:"credits"})]})}),e.jsxs("div",{className:`flex items-center space-x-1 ${o.color} mb-2`,children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${o.color.replace("text-","bg-")}`}),e.jsx("span",{className:"text-sm font-medium",children:o.status})]})]}),r<10&&e.jsxs(b.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(k,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm font-medium",children:"Low Balance Warning"})]}),e.jsx("p",{className:"text-red-300 text-sm mt-1",children:"Consider purchasing more credits to continue using AI features."})]})]}),e.jsxs(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Plan Type"}),e.jsx("span",{className:`font-medium ${a(l)}`,children:l})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Status"}),e.jsx("span",{className:`font-medium ${o.color}`,children:o.status})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Credits Available"}),e.jsx("span",{className:"text-white font-medium",children:r})]}),l.toLowerCase()==="free"&&e.jsx("div",{className:"pt-3 border-t border-border-secondary",children:e.jsx("p",{className:"text-gray-400 text-xs",children:"Upgrade to Basic or Pro for more credits and features"})})]})]})]})},W=({transactions:r,isLoading:l,onLoadMore:i})=>{const[a,d]=N.useState("all"),[h,o]=N.useState("date"),m=t=>t>0?e.jsx(U,{className:"w-4 h-4 text-red-400"}):e.jsx($,{className:"w-4 h-4 text-green-400"}),y=t=>t>0?"text-red-400":"text-green-400",f=t=>{const x=new Date(t);return{date:x.toLocaleDateString(),time:x.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},s=r.filter(t=>a==="used"?t.credits_used>0:a==="purchased"?t.credits_used<0:!0),n=[...s].sort((t,x)=>h==="date"?new Date(x.created_at).getTime()-new Date(t.created_at).getTime():Math.abs(x.credits_used)-Math.abs(t.credits_used)),c=()=>{const t=[["Date","Type","Credits","Operation","Description"].join(","),...n.map(p=>[new Date(p.created_at).toLocaleDateString(),p.credits_used>0?"Used":"Purchased",Math.abs(p.credits_used),p.operation_type,`"${p.description}"`].join(","))].join(`
`),x=new Blob([t],{type:"text/csv"}),g=window.URL.createObjectURL(x),j=document.createElement("a");j.href=g,j.download=`credit-history-${new Date().toISOString().split("T")[0]}.csv`,j.click(),window.URL.revokeObjectURL(g)};return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Transaction History"}),e.jsxs("p",{className:"text-gray-400 text-sm",children:[s.length," of ",r.length," transactions"]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:a,onChange:t=>d(t.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"used",children:"Credits Used"}),e.jsx("option",{value:"purchased",children:"Credits Purchased"})]}),e.jsx(_,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:h,onChange:t=>o(t.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"date",children:"Sort by Date"}),e.jsx("option",{value:"amount",children:"Sort by Amount"})]}),e.jsx(_,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs(w,{onClick:c,variant:"secondary",size:"sm",disabled:r.length===0,children:[e.jsx(E,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),e.jsx("div",{className:"space-y-3",children:l&&r.length===0?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((t,x)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-600 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-600 rounded w-1/2"})]}),e.jsx("div",{className:"h-6 bg-gray-600 rounded w-16"})]})})},x))}):n.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(k,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-400 mb-2",children:"No Transactions Found"}),e.jsx("p",{className:"text-gray-500",children:a==="all"?"You haven't made any credit transactions yet.":`No ${a} transactions found.`})]}):n.map((t,x)=>{const{date:g,time:j}=f(t.created_at),p=t.credits_used<0;return e.jsx(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:x*.05},className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`p-2 rounded-full ${p?"bg-green-500/20":"bg-red-500/20"}`,children:m(t.credits_used)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:t.operation_type.replace(/_/g," ").replace(/\b\w/g,C=>C.toUpperCase())}),e.jsx("p",{className:"text-gray-400 text-sm",children:t.description}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("span",{className:"text-gray-500 text-xs",children:g}),e.jsx("span",{className:"text-gray-600",children:"•"}),e.jsx("span",{className:"text-gray-500 text-xs",children:j})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:`font-semibold ${y(t.credits_used)}`,children:[p?"+":"-",Math.abs(t.credits_used)," credits"]}),t.study_set_id&&e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Study Set"})]})]})},t.id)})}),r.length>0&&r.length%50===0&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx(w,{onClick:i,variant:"secondary",isLoading:l,disabled:l,children:"Load More Transactions"})})]})},P=[{id:"study-buddy",name:"Study Buddy",credits:100,price:9.99,description:"Perfect for high school & early college students",features:["100 study credits (5,000 flashcards/quizzes)","Covers 2-3 classes per semester","Valid for 6 months","All AI study tools included","Perfect for finals prep"]},{id:"deans-list",name:"Dean's List",credits:500,price:39.99,bonus:50,popular:!0,description:"Most popular for serious undergrads & grad students",features:["500 study credits (25,000 flashcards/quizzes)","+50 bonus credits (2,500 extra generations)","Covers full semester workload","Valid for 12 months","Priority support for crunch time","All AI study tools included"]},{id:"phd-power",name:"PhD Power",credits:1e3,price:69.99,bonus:200,description:"Built for graduate students & research powerhouses",features:["1,000 study credits (50,000 flashcards/quizzes)","+200 bonus credits (10,000 extra generations)","Handles multiple courses + thesis prep","Valid for 12 months","Priority support","Early access to new AI features","All study tools included"]},{id:"academic-legend",name:"Academic Legend",credits:2500,price:149.99,bonus:750,description:"Ultimate package for study groups & academic overachievers",features:["2,500 study credits (125,000 flashcards/quizzes)","+750 bonus credits (37,500 extra generations)","Perfect for study groups & tutoring","Valid for 18 months","Dedicated academic support","Early access to new features","Custom study integrations","All AI study tools included"]}],G=({currentBalance:r,onPurchaseComplete:l})=>{const[i,a]=N.useState(null),[d,h]=N.useState(!1),{purchaseCredits:o}=D(),m=async s=>{const n=P.find(c=>c.id===s);if(n){h(!0),a(s);try{const c=await o(n.credits+(n.bonus||0));c.success?l():console.error("Purchase failed:",c.error)}catch(c){console.error("Purchase error:",c)}finally{h(!1),a(null)}}},y=s=>{switch(s){case"starter":return e.jsx(v,{className:"w-6 h-6"});case"popular":return e.jsx(F,{className:"w-6 h-6"});case"power":return e.jsx(H,{className:"w-6 h-6"});case"enterprise":return e.jsx(T,{className:"w-6 h-6"});default:return e.jsx(v,{className:"w-6 h-6"})}},f=(s,n,c)=>{if(!n)return null;const x=(s+n)/s*c,g=(x-c)/x*100;return Math.round(g)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Current Balance"}),e.jsx("p",{className:"text-gray-400",children:"Your available credits"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"text-2xl font-bold text-primary-400",children:r}),e.jsx("span",{className:"text-gray-400 ml-2",children:"credits"})]})]})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Power Up Your Studies"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Choose the perfect study package for your academic goals"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:P.map((s,n)=>{const c=s.credits+(s.bonus||0),t=s.bonus?f(s.credits,s.bonus,s.price):null,x=i===s.id,g=d&&x;return e.jsxs(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:n*.1},className:`
                  relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                  ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":"border-border-primary hover:border-border-secondary"}
                  ${x?"ring-2 ring-primary-500/50":""}
                `,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsx("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Most Popular"})}),t&&e.jsx("div",{className:"absolute -top-2 -right-2",children:e.jsxs("div",{className:"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:[t,"% OFF"]})}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`inline-flex p-3 rounded-lg mb-4 ${s.popular?"bg-primary-500/20 text-primary-400":"bg-background-tertiary text-gray-400"}`,children:y(s.id)}),e.jsx("h4",{className:"text-lg font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"text-3xl font-bold text-white",children:s.credits}),s.bonus&&e.jsxs("span",{className:"text-green-400 text-sm ml-1",children:["+",s.bonus]}),e.jsx("div",{className:"text-gray-400 text-sm",children:"credits"}),s.bonus&&e.jsxs("div",{className:"text-green-400 text-xs",children:["Total: ",c," credits"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("span",{className:"text-2xl font-bold text-white",children:["$",s.price]}),e.jsxs("div",{className:"text-gray-400 text-sm",children:["$",(s.price/c).toFixed(3)," per credit"]})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:s.description}),e.jsx("div",{className:"space-y-2 mb-6",children:s.features.map((j,p)=>e.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[e.jsx(I,{className:"w-4 h-4 text-green-400 mr-2 flex-shrink-0"}),e.jsx("span",{children:j})]},p))}),e.jsx(w,{onClick:()=>m(s.id),variant:s.popular?"primary":"secondary",className:"w-full",isLoading:g,disabled:d,children:g?"Processing...":`Purchase ${s.name}`})]})]},s.id)})})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Student-Friendly Payment"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Safe & Secure"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Student-safe payments through Stripe. Your payment info is never stored. Perfect for using your student card or parent's card with permission."})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Flexible Validity"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Credits last for the full duration shown - perfect for semester planning. No rush to use them all at once!"})]})]})]})]})},J=({stats:r,operationCosts:l})=>{const i=N.useMemo(()=>{const s=Array.from({length:7},(n,c)=>{const t=new Date;return t.setDate(t.getDate()-(6-c)),{date:t.toLocaleDateString("en-US",{weekday:"short"}),fullDate:t.toISOString().split("T")[0],credits:0}});return r!=null&&r.dailyUsage&&Array.isArray(r.dailyUsage)&&r.dailyUsage.forEach(n=>{const c=s.findIndex(t=>t.fullDate===n.date);c!==-1&&(s[c].credits=n.credits)}),s},[r==null?void 0:r.dailyUsage]),a=Math.max(...i.map(s=>s.credits),1),d=i.reduce((s,n)=>s+n.credits,0),h=d/7,o=i.slice(0,3).reduce((s,n)=>s+n.credits,0)/3,m=i.slice(4).reduce((s,n)=>s+n.credits,0)/3,y=m>o?"up":m<o?"down":"stable",f=o>0?Math.abs((m-o)/o*100):0;return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Usage Trends"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Last 7 days"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[y==="up"?e.jsx(L,{className:"w-5 h-5 text-green-400"}):y==="down"?e.jsx(z,{className:"w-5 h-5 text-red-400"}):null,y!=="stable"&&e.jsxs("span",{className:`text-sm font-medium ${y==="up"?"text-green-400":"text-red-400"}`,children:[f.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:i.map((s,n)=>{const c=a>0?s.credits/a*100:0;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full flex justify-center mb-2",children:e.jsx(b.div,{initial:{height:0},animate:{height:`${c}%`},transition:{duration:.5,delay:n*.1},className:"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group",style:{minHeight:c>0?"4px":"0px"},children:e.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsxs("div",{className:"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap",children:[s.credits," credits"]})})})}),e.jsx("span",{className:"text-xs text-gray-400",children:s.date})]},s.date)})})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:d}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Total This Week"})]})}),e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:h.toFixed(1)}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Daily Average"})]})})]}),e.jsxs("div",{className:"mt-4 pt-4 border-t border-border-secondary",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Usage Efficiency"}),e.jsx("div",{className:"space-y-2",children:r!=null&&r.usageByOperation?Object.entries(r.usageByOperation).slice(0,3).map(([s,n])=>{const c=l==null?void 0:l.find(x=>x.operation_type===s),t=c?Math.floor(n/c.credits_required):0;return e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:s.replace(/_/g," ")}),e.jsxs("span",{className:"text-white",children:[t," generations"]})]},s)}):e.jsx("div",{className:"text-gray-400 text-sm text-center py-2",children:"No usage data available"})})]})]})},K=[{id:"overview",label:"Overview",icon:q,description:"Credit balance and usage summary"},{id:"history",label:"Transaction History",icon:k,description:"Detailed credit transaction log"},{id:"purchase",label:"Buy Credits",icon:V,description:"Purchase additional credits"}],X=()=>{const[r,l]=N.useState("overview"),{user:i}=O(),{balance:a,transactions:d,operationCosts:h,stats:o,isLoading:m,error:y,fetchBalance:f,fetchTransactions:s,fetchOperationCosts:n,fetchStats:c,clearError:t}=D();N.useEffect(()=>{f(),s(),n(),c()},[f,s,n,c]);const x=async()=>{t(),await Promise.all([f(),s(),n(),c()])},g=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(Y,{balance:a,userTier:(i==null?void 0:i.subscription_tier)||"Free",isLoading:m}),o&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(J,{stats:o,operationCosts:h}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Usage Breakdown"}),e.jsx("div",{className:"space-y-3",children:o!=null&&o.usageByOperation?Object.entries(o.usageByOperation).map(([u,S])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:u.replace(/_/g," ")}),e.jsxs("span",{className:"text-white font-medium",children:[S," credits"]})]},u)):e.jsx("div",{className:"text-gray-400 text-center py-4",children:"No usage data available"})})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Credit Costs"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(h==null?void 0:h.length)>0?h.map(u=>e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary",children:[e.jsx("h4",{className:"font-medium text-white capitalize mb-2",children:u.operation_type.replace(/_/g," ")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(v,{className:"w-4 h-4 text-primary-400"}),e.jsxs("span",{className:"text-primary-400 font-semibold",children:[u.credits_required," credits"]})]})]},u.operation_type)):e.jsx("div",{className:"col-span-full text-gray-400 text-center py-8",children:"No operation cost data available"})})]})]}),j=()=>e.jsx(W,{transactions:d,isLoading:m,onLoadMore:()=>s(50,d.length)}),p=()=>e.jsx(G,{currentBalance:a,userTier:(i==null?void 0:i.subscription_tier)||"Free",onPurchaseComplete:x}),C=()=>{switch(r){case"overview":return g();case"history":return j();case"purchase":return p();default:return g()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Credits"}),e.jsx("p",{className:"text-gray-400",children:"Manage your AI generation credits"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(w,{onClick:x,variant:"secondary",disabled:m,children:[e.jsx(M,{className:`w-4 h-4 mr-2 ${m?"animate-spin":""}`}),"Refresh"]})})]}),y&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(R,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:y}),e.jsx(w,{onClick:t,variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:K.map(u=>{const S=u.icon,B=r===u.id;return e.jsxs("button",{onClick:()=>l(u.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${B?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(S,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:u.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:u.description})]})]},u.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(b.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:C()},r)})]})]})})};export{X as CreditsPage};
