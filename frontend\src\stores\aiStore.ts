import { create } from 'zustand';
import { StudySet, Flashcard, QuizQuestion, DifficultyLevel, ContentLength } from '../../../shared/types';

interface AIGenerationState {
  isGenerating: boolean;
  generationProgress: string;
  lastGenerated: {
    studySet?: StudySet;
    content?: Flashcard[] | QuizQuestion[];
    type?: 'flashcards' | 'quiz';
  } | null;

  // Actions
  generateFlashcards: (params: {
    documentIds: string[];
    name: string;
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
  }) => Promise<{ studySet: StudySet; flashcards: Flashcard[]; creditsRemaining: number }>;

  generateQuiz: (params: {
    documentIds: string[];
    name: string;
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
  }) => Promise<{ studySet: StudySet; questions: QuizQuestion[]; creditsRemaining: number }>;

  generateMoreFlashcards: (params: {
    studySetId: string;
    documentIds: string[];
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
  }) => Promise<{ flashcards: Flashcard[]; creditsRemaining: number }>;

  clearLastGenerated: () => void;
}

export const useAIStore = create<AIGenerationState>((set) => ({
  isGenerating: false,
  generationProgress: '',
  lastGenerated: null,

  generateFlashcards: async (params) => {
    set({ isGenerating: true, generationProgress: 'Preparing documents...' });
    
    try {
      const token = localStorage.getItem('auth_token');
      
      set({ generationProgress: 'Generating flashcards with AI...' });
      
      const response = await fetch('/api/ai/generate-flashcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Generation failed');
      }

      const result = await response.json();
      
      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.flashcards,
            type: 'flashcards'
          },
          isGenerating: false,
          generationProgress: ''
        });
        
        return {
          studySet: result.data.studySet,
          flashcards: result.data.flashcards,
          creditsRemaining: result.data.creditsRemaining
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: '' });
      throw error;
    }
  },

  generateQuiz: async (params) => {
    set({ isGenerating: true, generationProgress: 'Preparing documents...' });
    
    try {
      const token = localStorage.getItem('auth_token');
      
      set({ generationProgress: 'Generating quiz questions with AI...' });
      
      const response = await fetch('/api/ai/generate-quiz', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Generation failed');
      }

      const result = await response.json();
      
      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.questions,
            type: 'quiz'
          },
          isGenerating: false,
          generationProgress: ''
        });
        
        return {
          studySet: result.data.studySet,
          questions: result.data.questions,
          creditsRemaining: result.data.creditsRemaining
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: '' });
      throw error;
    }
  },

  generateMoreFlashcards: async (params) => {
    set({ isGenerating: true, generationProgress: 'Preparing documents...' });

    try {
      const token = localStorage.getItem('auth_token');

      set({ generationProgress: 'Generating additional flashcards with AI...' });

      const response = await fetch('/api/ai/generate-more-flashcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Generation failed');
      }

      const result = await response.json();

      if (result.success) {
        set({
          isGenerating: false,
          generationProgress: ''
        });

        return {
          flashcards: result.data.flashcards,
          creditsRemaining: result.data.creditsRemaining
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: '' });
      throw error;
    }
  },

  clearLastGenerated: () => {
    set({ lastGenerated: null });
  }
}));
