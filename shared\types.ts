// ----------------------------------------
// User & Authentication Types
// ----------------------------------------

export interface UserProfile {
  id: string; // Corresponds to Supabase auth.uid()
  email: string;
  name?: string;
  subscription_tier: "Free" | "Basic" | "Pro";
  credits_remaining: number;
  is_active: boolean;
  last_login?: string;
  subscription_expires_at?: string; // ISO 8601 date string
  stripe_customer_id?: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
}

export interface AuthResult {
  success: boolean;
  user?: UserProfile;
  token?: string;
  error?: string;
}

// ----------------------------------------
// Document Management Types
// ----------------------------------------

export type DocumentFileType = "pdf" | "docx" | "txt" | "pptx";

export interface DocumentMetadata {
  id: string;
  user_id: string;
  filename: string;
  file_type: DocumentFileType;
  file_size: number; // in bytes
  supabase_storage_path: string;
  uploaded_at: string; // ISO 8601 date string
  is_processed: boolean;
  processing_error?: string;
}

export interface DocumentWithContent extends DocumentMetadata {
  content_text: string;
}

// ----------------------------------------
// Study Sets & Content Types
// ----------------------------------------

export type StudySetType = "flashcards" | "quiz";

// ----------------------------------------
// Difficulty and Content Length Enums
// ----------------------------------------

export enum DifficultyLevel {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  COLLEGE = 'college',
  GRADUATE = 'graduate',
  PHD = 'phd'
}

export enum ContentLength {
  SHORT = 'short',
  MEDIUM = 'medium',
  LONG = 'long'
}

// Type aliases for database compatibility
export type DifficultyLevelType = keyof typeof DifficultyLevel;
export type ContentLengthType = keyof typeof ContentLength;

export interface StudySet {
  id: string;
  user_id: string;
  name: string;
  type: StudySetType;
  is_ai_generated: boolean;
  source_documents?: { id: string; filename: string }[]; // Array of document IDs and names
  custom_prompt?: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  flashcard_count?: number;
  quiz_question_count?: number;
  last_studied_at?: string; // ISO 8601 date string
}

export interface Flashcard {
  id: string;
  study_set_id: string;
  front: string;
  back: string;
  is_flagged: boolean;
  is_ai_generated: boolean;
  difficulty_level?: DifficultyLevel;
  content_length?: ContentLength;
  times_reviewed: number;
  last_reviewed_at?: string; // ISO 8601 date string
}

export type QuestionType =
  | "multiple_choice"
  | "select_all"
  | "true_false"
  | "short_answer";

export interface QuizQuestion {
  id: string;
  study_set_id: string;
  question_text: string;
  question_type: QuestionType;
  options?: string[]; // For multiple_choice, select_all
  correct_answers: string[]; // Can be ['True'] or ['False'] for true_false
  explanation?: string;
  is_ai_generated: boolean;
  difficulty_level?: DifficultyLevel;
  content_length?: ContentLength;
  times_attempted: number;
  times_correct: number;
}

// ----------------------------------------
// Credit System Types
// ----------------------------------------

export interface CreditTransaction {
  id: string;
  user_id: string;
  credits_used: number; // Negative for additions
  operation_type: string;
  description: string;
  metadata?: Record<string, any>;
  study_set_id?: string;
  created_at: string; // ISO 8601 date string
}

export interface AIOperationCost {
  operation_type: string;
  credits_required: number;
  operations_per_credit: number;
  is_active: boolean;
}

// ----------------------------------------
// API Response Types
// ----------------------------------------

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// ----------------------------------------
// Component Props Types
// ----------------------------------------

export interface BaseComponentProps {
  className?: string;
  "data-testid"?: string;
}

// Button component props
export interface ButtonProps extends BaseComponentProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
}

// Input component props
export interface InputProps extends BaseComponentProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  type?: "text" | "email" | "password" | "number";
  error?: string;
  required?: boolean;
  disabled?: boolean;
}

// Modal component props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
}

// ----------------------------------------
// Study Session Types
// ----------------------------------------

export interface StudySession {
  id: string;
  user_id: string;
  study_set_id: string;
  session_type: StudySetType;
  started_at: string;
  ended_at?: string;
  total_items: number;
  completed_items: number;
  correct_answers?: number; // For quiz sessions
  session_data?: Record<string, any>; // Store session-specific data
}

export interface StudyProgress {
  study_set_id: string;
  total_flashcards?: number;
  reviewed_flashcards?: number;
  flagged_flashcards?: number;
  total_quiz_questions?: number;
  answered_questions?: number;
  correct_answers?: number;
  last_studied_at?: string;
  study_streak?: number;
}

// ----------------------------------------
// AI Generation Types
// ----------------------------------------

export interface AIGenerationRequest {
  documents: string[]; // Document IDs
  studySetType: StudySetType;
  customPrompt?: string;
  itemCount?: number;
  difficultyLevel?: DifficultyLevel;
  contentLength?: ContentLength;
}

export interface AIGenerationResult {
  studySetId: string;
  itemsGenerated: number;
  creditsUsed: number;
  processingTime: number;
}

// Helper functions for enum conversions
export const difficultyLevelToString = (level: DifficultyLevel): string => {
  const labels: Record<DifficultyLevel, string> = {
    [DifficultyLevel.EASY]: 'Easy',
    [DifficultyLevel.MEDIUM]: 'Medium',
    [DifficultyLevel.HARD]: 'Hard',
    [DifficultyLevel.COLLEGE]: 'College',
    [DifficultyLevel.GRADUATE]: 'Graduate',
    [DifficultyLevel.PHD]: 'PhD'
  };
  return labels[level];
};

export const contentLengthToString = (length: ContentLength): string => {
  const labels: Record<ContentLength, string> = {
    [ContentLength.SHORT]: 'Short',
    [ContentLength.MEDIUM]: 'Medium',
    [ContentLength.LONG]: 'Long'
  };
  return labels[length];
};

export const stringToDifficultyLevel = (str: string): DifficultyLevel => {
  const normalized = str.toLowerCase();
  switch (normalized) {
    case 'easy': return DifficultyLevel.EASY;
    case 'medium': return DifficultyLevel.MEDIUM;
    case 'hard': return DifficultyLevel.HARD;
    case 'college': return DifficultyLevel.COLLEGE;
    case 'graduate': return DifficultyLevel.GRADUATE;
    case 'phd': return DifficultyLevel.PHD;
    default: return DifficultyLevel.MEDIUM;
  }
};

export const stringToContentLength = (str: string): ContentLength => {
  const normalized = str.toLowerCase();
  switch (normalized) {
    case 'short': return ContentLength.SHORT;
    case 'medium': return ContentLength.MEDIUM;
    case 'long': return ContentLength.LONG;
    default: return ContentLength.MEDIUM;
  }
};

// Helper functions for number conversions (for backward compatibility)
export const difficultyLevelToNumber = (level: DifficultyLevel): number => {
  const mapping: Record<DifficultyLevel, number> = {
    [DifficultyLevel.EASY]: 1,
    [DifficultyLevel.MEDIUM]: 3,
    [DifficultyLevel.HARD]: 4,
    [DifficultyLevel.COLLEGE]: 5,
    [DifficultyLevel.GRADUATE]: 6,
    [DifficultyLevel.PHD]: 7
  };
  return mapping[level];
};

export const numberToDifficultyLevel = (num: number): DifficultyLevel => {
  switch (num) {
    case 1: return DifficultyLevel.EASY;
    case 2: return DifficultyLevel.EASY; // Map 2 to easy for backward compatibility
    case 3: return DifficultyLevel.MEDIUM;
    case 4: return DifficultyLevel.HARD;
    case 5: return DifficultyLevel.COLLEGE;
    case 6: return DifficultyLevel.GRADUATE;
    case 7: return DifficultyLevel.PHD;
    default: return DifficultyLevel.MEDIUM;
  }
};

// ----------------------------------------
// Utility Types
// ----------------------------------------

export type LoadingState = "idle" | "loading" | "success" | "error";

export interface ErrorState {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

export type SortDirection = "asc" | "desc";

export interface SortConfig {
  field: string;
  direction: SortDirection;
}

export interface FilterConfig {
  field: string;
  value: any;
  operator?: "eq" | "ne" | "gt" | "lt" | "gte" | "lte" | "in" | "like";
}
