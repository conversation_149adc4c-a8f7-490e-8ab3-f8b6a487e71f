export interface UserSettings {
    id: string;
    user_id: string;
    skip_delete_confirmations: boolean;
    shuffle_flashcards: boolean;
    created_at: string;
    updated_at: string;
}
export interface UserSettingsUpdate {
    skip_delete_confirmations?: boolean;
    shuffle_flashcards?: boolean;
}
declare class UserSettingsService {
    getUserSettings(userId: string): Promise<UserSettings>;
    createDefaultSettings(userId: string): Promise<UserSettings>;
    updateUserSettings(userId: string, updates: UserSettingsUpdate): Promise<UserSettings>;
    deleteUserSettings(userId: string): Promise<void>;
}
export declare const userSettingsService: UserSettingsService;
export {};
//# sourceMappingURL=userSettingsService.d.ts.map