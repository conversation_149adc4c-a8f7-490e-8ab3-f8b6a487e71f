import{r as l,j as e,D as F,S as le,B as x,C as Y,n as V,L as ce,K as G,p as $,A as J,T as se,E as R,i as W,U as Z,V as H,H as B,d as L,W as K,I as M,X as Q,_,b as oe,Y as de,Z as me,$ as xe,a0 as ue,a1 as he,y as pe,x as fe}from"./index-5f534378.js";import{D as ge,a as be}from"./DifficultySelector-9200e0ca.js";const ee=[{id:"free",name:"Study Starter",price:0,interval:"month",features:["500 AI study generations per month","Basic flashcards and quizzes","Up to 5 document uploads","Basic study analytics","Perfect for trying out ChewyAI"]},{id:"pro_monthly",name:"Scholar Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Perfect for serious students"]},{id:"pro_yearly",name:"Academic Year Pass",price:99.99,interval:"year",features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Save $20 vs monthly (2 months free!)","Perfect for the full academic year"]}],ye=()=>{var h,N;const[a,S]=l.useState(null),[j,d]=l.useState(!0),[g,c]=l.useState(!1),[b,n]=l.useState(null);l.useEffect(()=>{o()},[]);const o=async()=>{d(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch subscription data");const r=await t.json();if(r.success)S(r.data);else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to load subscription data"),S({currentPlan:ee[0],status:"active",nextBillingDate:new Date(Date.now()+30*24*60*60*1e3).toISOString()})}finally{d(!1)}},f=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({planId:s})});if(!r.ok)throw new Error("Failed to change subscription plan");const p=await r.json();if(p.success)await o();else throw new Error(p.error)}catch(t){n(t instanceof Error?t.message:"Failed to change subscription plan")}finally{c(!1)}},u=async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to cancel subscription");const r=await t.json();if(r.success)await o();else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to cancel subscription")}finally{c(!1)}}},m=async()=>{c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to reactivate subscription");const r=await t.json();if(r.success)await o();else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to reactivate subscription")}finally{c(!1)}};return j?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(s=>e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"},s))})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b})]}),a&&e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),e.jsx("p",{className:"text-gray-400",children:((h=a.currentPlan)==null?void 0:h.name)||"No active plan"})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a.status==="active"?"bg-green-500/20 text-green-400":a.status==="canceled"?"bg-red-500/20 text-red-400":a.status==="past_due"?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&e.jsxs("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[e.jsx(le,{className:"w-4 h-4"}),e.jsx("span",{children:a.cancelAtPeriodEnd?`Access ends on ${new Date(a.nextBillingDate).toLocaleDateString()}`:`Next billing date: ${new Date(a.nextBillingDate).toLocaleDateString()}`})]}),((N=a.currentPlan)==null?void 0:N.id)!=="free"&&e.jsxs("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?e.jsx(x,{onClick:m,isLoading:g,variant:"primary",size:"sm",children:"Reactivate Subscription"}):e.jsx(x,{onClick:u,isLoading:g,variant:"danger",size:"sm",children:"Cancel Subscription"}),e.jsxs(x,{onClick:o,variant:"secondary",size:"sm",children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:ee.map(s=>{var r;const t=((r=a==null?void 0:a.currentPlan)==null?void 0:r.id)===s.id;return e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":t?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"}`,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(ce,{className:"w-3 h-3"}),e.jsx("span",{children:"Most Popular"})]})}),t&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsxs("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(G,{className:"w-3 h-3"}),e.jsx("span",{children:"Current"})]})}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h5",{className:"text-xl font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.price,e.jsxs("span",{className:"text-lg text-gray-400",children:["/",s.interval]})]})]}),e.jsx("ul",{className:"space-y-3 mb-6",children:s.features.map((p,w)=>e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx(G,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-300 text-sm",children:p})]},w))}),e.jsx(x,{onClick:()=>f(s.id),disabled:t||g,isLoading:g,variant:s.popular?"primary":"secondary",className:"w-full",children:t?"Current Plan":`Switch to ${s.name}`})]},s.id)})})]})]})})},je=()=>{const[a,S]=l.useState(!1),[j,d]=l.useState(!1),[g,c]=l.useState(null),[b,n]=l.useState(null),[o,f]=l.useState({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[u,m]=l.useState({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),h=async()=>{S(!0),c(null),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/data/export",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({exportData:u})});if(!r.ok)throw new Error("Failed to export data");const p=await r.blob(),w=window.URL.createObjectURL(p),C=document.createElement("a");C.href=w,C.download=`chewyai-data-export-${new Date().toISOString().split("T")[0]}.json`,C.click(),window.URL.revokeObjectURL(w),n("Data exported successfully!")}catch(t){c(t instanceof Error?t.message:"Failed to export data")}finally{S(!1)}},N=async t=>{if(confirm(`Are you sure you want to clear all ${t}? This action cannot be undone.`)){d(!0),c(null),n(null);try{const r=localStorage.getItem("auth_token"),p=await fetch(`/api/data/clear/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`}});if(!p.ok)throw new Error(`Failed to clear ${t}`);const w=await p.json();if(w.success)n(`${t} cleared successfully!`),await s();else throw new Error(w.error)}catch(r){c(r instanceof Error?r.message:`Failed to clear ${t}`)}finally{d(!1)}}},s=async()=>{try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/data/stats",{headers:{Authorization:`Bearer ${t}`}});if(r.ok){const p=await r.json();p.success&&f(p.data)}}catch{}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),g&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:g})]}),b&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:b})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),e.jsxs(x,{onClick:s,variant:"secondary",size:"sm",children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:o.studySets}),e.jsx("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:o.flashcards}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:o.quizzes}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:o.documents}),e.jsx("div",{className:"text-sm text-gray-400",children:"Documents"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:o.totalSize}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(J,{className:"w-6 h-6 text-blue-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsx("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(u).map(([t,r])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm text-gray-300 capitalize",children:t.replace(/([A-Z])/g," $1").trim()}),e.jsx("button",{onClick:()=>m({...u,[t]:!r}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r?"translate-x-6":"translate-x-1"}`})})]},t))]}),e.jsxs(x,{onClick:h,isLoading:a,variant:"primary",children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(se,{className:"w-6 h-6 text-red-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>N("study-sets"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),e.jsx(x,{onClick:()=>N("flashcards"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>N("quizzes"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),e.jsx(x,{onClick:()=>N("analytics"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},Ne=()=>{const[a,S]=l.useState(null),[j,d]=l.useState(!0),[g,c]=l.useState(!1),[b,n]=l.useState(null);l.useEffect(()=>{o()},[]);const o=async()=>{d(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/billing",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch billing data");const r=await t.json();if(r.success)S(r.data);else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to load billing data"),S({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-30*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:new Date().toISOString(),dueDate:new Date(Date.now()+7*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+30*24*60*60*1e3).toISOString()}})}finally{d(!1)}},f=async s=>{try{const t=localStorage.getItem("auth_token"),r=await fetch(`/api/billing/invoices/${s}/download`,{headers:{Authorization:`Bearer ${t}`}});if(!r.ok)throw new Error("Failed to download invoice");const p=await r.blob(),w=window.URL.createObjectURL(p),C=document.createElement("a");C.href=w,C.download=`invoice-${s}.pdf`,C.click(),window.URL.revokeObjectURL(w)}catch(t){n(t instanceof Error?t.message:"Failed to download invoice")}},u=async()=>{n("Payment method management coming soon")},m=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:s})});if(!r.ok)throw new Error("Failed to update default payment method");const p=await r.json();if(p.success)await o();else throw new Error(p.error)}catch(t){n(t instanceof Error?t.message:"Failed to update payment method")}finally{c(!1)}},h=s=>{switch(s){case"paid":return e.jsx($,{className:"w-5 h-5 text-green-400"});case"pending":return e.jsx(W,{className:"w-5 h-5 text-yellow-400"});case"failed":return e.jsx(Z,{className:"w-5 h-5 text-red-400"});default:return e.jsx(W,{className:"w-5 h-5 text-gray-400"})}},N=s=>{switch(s){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return j?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded-lg"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),e.jsxs(x,{onClick:o,variant:"secondary",size:"sm",children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),e.jsx(x,{onClick:u,variant:"secondary",size:"sm",children:"Add Payment Method"})]}),(a==null?void 0:a.paymentMethods.length)===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(R,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"No payment methods added"})]}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.paymentMethods.map(s=>{var t;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(R,{className:"w-6 h-6 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-white font-medium",children:[(t=s.brand)==null?void 0:t.toUpperCase()," •••• ",s.last4]}),s.isDefault&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),s.expiryMonth&&s.expiryYear&&e.jsxs("p",{className:"text-gray-400 text-sm",children:["Expires ",s.expiryMonth.toString().padStart(2,"0"),"/",s.expiryYear]})]})]}),!s.isDefault&&e.jsx(x,{onClick:()=>m(s.id),isLoading:g,variant:"secondary",size:"sm",children:"Set as Default"})]},s.id)})})]}),(a==null?void 0:a.nextInvoice)&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("p",{className:"text-blue-300",children:["$",a.nextInvoice.amount," ",a.nextInvoice.currency.toUpperCase()]}),e.jsxs("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(a.nextInvoice.date).toLocaleDateString()]})]})})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),(a==null?void 0:a.invoices.length)===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-400",children:"No invoices found"})}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.invoices.map(s=>e.jsxs(V.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[h(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-white font-medium",children:s.number}),e.jsx("span",{className:`text-sm font-medium ${N(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description}),e.jsxs("p",{className:"text-gray-500 text-xs",children:[new Date(s.date).toLocaleDateString(),s.dueDate&&s.status==="pending"&&e.jsxs("span",{children:[" • Due ",new Date(s.dueDate).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:["$",s.amount," ",s.currency.toUpperCase()]}),s.status==="paid"&&e.jsxs(x,{onClick:()=>f(s.id),variant:"secondary",size:"sm",children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},s.id))})]})]})})},we=({enabled:a,onToggle:S})=>{const[j,d]=l.useState(!1),[g,c]=l.useState(!1),[b,n]=l.useState(null),[o,f]=l.useState(null),[u,m]=l.useState(null),[h,N]=l.useState(""),[s,t]=l.useState(!1),[r,p]=l.useState(!1),w=async()=>{c(!0),n(null),f(null);try{const{createClient:y}=await _(()=>import("./index-5f534378.js").then(D=>D.a2),["assets/index-5f534378.js","assets/index-f516001c.css"]),I=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:T,error:P}=await I.auth.mfa.enroll({factorType:"totp",friendlyName:"ChewyAI Authenticator"});if(P)throw new Error(P.message);m({qrCode:T.totp.qr_code,secret:T.totp.secret,backupCodes:[],factorId:T.id}),d(!0)}catch(y){n(y instanceof Error?y.message:"Failed to setup 2FA"),m({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",factorId:"mock-factor-id",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),d(!0)}finally{c(!1)}},C=async()=>{if(!h||h.length!==6){n("Please enter a valid 6-digit code");return}c(!0),n(null);try{const{createClient:y}=await _(()=>import("./index-5f534378.js").then(O=>O.a2),["assets/index-5f534378.js","assets/index-f516001c.css"]),I=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI");if(!u)throw new Error("No setup data available");const{data:T,error:P}=await I.auth.mfa.challenge({factorId:u.factorId});if(P)throw new Error(P.message);const{error:D}=await I.auth.mfa.verify({factorId:u.factorId,challengeId:T.id,code:h});if(D)throw new Error(D.message);f("Two-factor authentication enabled successfully!"),d(!1),p(!0),S(!0)}catch(y){n(y instanceof Error?y.message:"Failed to verify 2FA code")}finally{c(!1)}},A=async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){c(!0),n(null);try{const{createClient:y}=await _(()=>import("./index-5f534378.js").then(D=>D.a2),["assets/index-5f534378.js","assets/index-f516001c.css"]),I=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:T,error:P}=await I.auth.mfa.listFactors();if(P)throw new Error(P.message);for(const D of T.totp){const{error:O}=await I.auth.mfa.unenroll({factorId:D.id});O&&console.error("Failed to unenroll factor:",O)}f("Two-factor authentication disabled successfully"),S(!1)}catch(y){n(y instanceof Error?y.message:"Failed to disable 2FA")}finally{c(!1)}}},E=y=>{navigator.clipboard.writeText(y),f("Copied to clipboard!"),setTimeout(()=>f(null),2e3)},z=()=>{d(!1),m(null),N(""),n(null),f(null)};return j&&u?e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx(H,{className:"w-6 h-6 text-primary-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),b&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:b})]})}),o&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:o})]})}),r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx($,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),e.jsx("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),e.jsx("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),e.jsx("div",{className:"grid grid-cols-2 gap-2 mb-4",children:u.backupCodes.map((y,I)=>e.jsx("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:e.jsx("code",{className:"text-primary-400 font-mono",children:y})},I))}),e.jsxs(x,{onClick:()=>E(u.backupCodes.join(`
`)),variant:"secondary",size:"sm",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),e.jsx(x,{onClick:()=>p(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block",children:e.jsx("img",{src:u.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:e.jsx("code",{className:"text-primary-400 font-mono",children:s?u.secret:"••••••••••••••••"})}),e.jsx(x,{onClick:()=>t(!s),variant:"secondary",size:"sm",children:s?e.jsx(B,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})}),e.jsx(x,{onClick:()=>E(u.secret),variant:"secondary",size:"sm",children:e.jsx(K,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(M,{value:h,onChange:N,placeholder:"123456",className:"flex-1"}),e.jsx(x,{onClick:C,isLoading:g,disabled:h.length!==6,children:"Verify"})]})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsx(x,{onClick:z,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(H,{className:`w-6 h-6 ${a?"text-green-400":"text-gray-400"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a?"Enabled":"Disabled"})]}),b&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:b})]})}),o&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:o})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-300 text-sm",children:a?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),e.jsx("div",{className:"flex space-x-3",children:a?e.jsx(x,{onClick:A,isLoading:g,variant:"danger",children:"Disable 2FA"}):e.jsxs(x,{onClick:w,isLoading:g,variant:"primary",children:[e.jsx(Q,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},ve=({isOpen:a,onClose:S,onSuccess:j})=>{const[d,g]=l.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[c,b]=l.useState({current:!1,new:!1,confirm:!1}),[n,o]=l.useState(!1),[f,u]=l.useState(null),[m,h]=l.useState(null),N=(A,E)=>{g(z=>({...z,[A]:E})),u(null)},s=A=>E=>{N(A,E)},t=A=>{b(E=>({...E,[A]:!E[A]}))},r=()=>d.currentPassword?d.newPassword?d.newPassword.length<8?(u("New password must be at least 8 characters long"),!1):d.newPassword!==d.confirmPassword?(u("New passwords do not match"),!1):d.currentPassword===d.newPassword?(u("New password must be different from current password"),!1):!0:(u("New password is required"),!1):(u("Current password is required"),!1),p=async A=>{if(A.preventDefault(),!!r()){o(!0),u(null),h(null);try{const E=localStorage.getItem("auth_token"),z=await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({currentPassword:d.currentPassword,newPassword:d.newPassword})}),y=await z.json();if(!z.ok)throw new Error(y.error||"Failed to change password");h("Password changed successfully!"),setTimeout(()=>{j(),S(),w()},1500)}catch(E){u(E instanceof Error?E.message:"Failed to change password")}finally{o(!1)}}},w=()=>{g({currentPassword:"",newPassword:"",confirmPassword:""}),b({current:!1,new:!1,confirm:!1}),u(null),h(null)},C=()=>{w(),S()};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Q,{className:"w-5 h-5 text-primary-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Change Password"})]}),e.jsx("button",{onClick:C,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Z,{className:"w-5 h-5"})})]}),e.jsxs("form",{onSubmit:p,className:"p-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:c.current?"text":"password",value:d.currentPassword,onChange:s("currentPassword"),placeholder:"Enter your current password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("current"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.current?e.jsx(B,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:c.new?"text":"password",value:d.newPassword,onChange:s("newPassword"),placeholder:"Enter your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("new"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.new?e.jsx(B,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:c.confirm?"text":"password",value:d.confirmPassword,onChange:s("confirmPassword"),placeholder:"Confirm your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("confirm"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.confirm?e.jsx(B,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]})]}),f&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:f})}),m&&e.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-green-400 text-sm",children:m})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{type:"button",variant:"secondary",onClick:C,disabled:n,className:"flex-1",children:"Cancel"}),e.jsx(x,{type:"submit",variant:"primary",isLoading:n,disabled:n,className:"flex-1",children:n?"Changing...":"Change Password"})]})]})]})}):null},Se=({isOpen:a,onClose:S,action:j})=>{const[d,g]=l.useState(""),[c,b]=l.useState(!1),[n,o]=l.useState(null),[f,u]=l.useState("confirm"),h={deactivate:{title:"Deactivate Account",description:"Your account will be temporarily disabled. You can reactivate it by logging in again.",confirmText:"DEACTIVATE",buttonText:"Deactivate Account",warningText:"This will temporarily disable your account and log you out.",endpoint:"/api/auth/deactivate-account"},delete:{title:"Delete Account",description:"This will permanently delete your account and all associated data. This action cannot be undone.",confirmText:"DELETE FOREVER",buttonText:"Delete Account Forever",warningText:"This will permanently delete all your data including study sets, flashcards, progress, and subscription information.",endpoint:"/api/auth/delete-account"}}[j],N=d===h.confirmText,s=async()=>{if(f==="confirm"){u("final");return}if(!N){o(`Please type "${h.confirmText}" to confirm`);return}b(!0),o(null);try{const p=localStorage.getItem("auth_token"),w=await fetch(h.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${p}`},body:JSON.stringify({confirmation:d})}),C=await w.json();if(!w.ok)throw new Error(C.error||`Failed to ${j} account`);localStorage.removeItem("auth_token"),localStorage.removeItem("user_data"),window.location.href="/login"}catch(p){o(p instanceof Error?p.message:`Failed to ${j} account`)}finally{b(!1)}},t=()=>{g(""),o(null),u("confirm"),S()},r=()=>{u("confirm"),o(null)};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:h.title})]}),e.jsx("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Z,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6",children:f==="confirm"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-400 mb-2",children:"Warning"}),e.jsx("p",{className:"text-gray-300 text-sm",children:h.warningText})]})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-300 text-sm mb-4",children:h.description}),j==="delete"&&e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("p",{children:"This will delete:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"All study sets and flashcards"}),e.jsx("li",{children:"Quiz history and progress"}),e.jsx("li",{children:"Account settings and preferences"}),e.jsx("li",{children:"Subscription and billing information"}),e.jsx("li",{children:"All uploaded documents"})]})]})]}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:t,className:"flex-1",children:"Cancel"}),e.jsx(x,{variant:"danger",onClick:s,className:"flex-1",children:"Continue"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(se,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-red-400",children:"Final Confirmation"})]})}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-300 text-sm mb-4",children:["To confirm this action, please type ",e.jsx("span",{className:"font-mono font-bold text-red-400",children:h.confirmText})," in the box below:"]}),e.jsx(M,{type:"text",value:d,onChange:p=>{g(p),o(null)},placeholder:h.confirmText,className:"font-mono",disabled:c})]}),n&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:n})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:r,disabled:c,className:"flex-1",children:"Back"}),e.jsx(x,{variant:"danger",onClick:s,isLoading:c,disabled:c||!N,className:"flex-1",children:c?"Processing...":h.buttonText})]})]})})]})}):null},ke=[{id:"profile",label:"Profile",icon:de,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:me,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:xe,description:"Control notification settings"},{id:"security",label:"Security",icon:H,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:R,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:R,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:ue,description:"Export, import, and manage your data"}],Ae=()=>{const[a,S]=l.useState("profile"),[j,d]=l.useState(!1),[g,c]=l.useState(null),[b,n]=l.useState(null),{user:o}=oe(),[f,u]=l.useState({name:(o==null?void 0:o.name)||"",email:(o==null?void 0:o.email)||"",bio:"",avatar:null}),[m,h]=l.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:ge.MEDIUM}),[N,s]=l.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[t,r]=l.useState({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}),[p,w]=l.useState(!1),[C,A]=l.useState(!1),[E,z]=l.useState("deactivate");l.useEffect(()=>{(async()=>{try{d(!0)}catch{c("Failed to load user settings")}finally{d(!1)}})()},[]);const y=async()=>{d(!0),c(null),n(null);try{const i=new FormData;i.append("name",f.name),i.append("bio",f.bio),f.avatar&&i.append("avatar",f.avatar);const k=localStorage.getItem("auth_token"),v=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:`Bearer ${k}`},body:i});if(!v.ok)throw new Error("Failed to update profile");const U=await v.json();if(U.success)n("Profile updated successfully!");else throw new Error(U.error)}catch(i){c(i instanceof Error?i.message:"Failed to update profile")}finally{d(!1)}},I=async()=>{d(!0),c(null),n(null);try{const i=localStorage.getItem("auth_token"),k=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify(m)});if(!k.ok)throw new Error("Failed to update preferences");const v=await k.json();if(v.success)n("Preferences updated successfully!");else throw new Error(v.error)}catch(i){c(i instanceof Error?i.message:"Failed to update preferences")}finally{d(!1)}},T=async()=>{d(!0),c(null),n(null);try{const i=localStorage.getItem("auth_token"),k=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify(N)});if(!k.ok)throw new Error("Failed to update notification settings");const v=await k.json();if(v.success)n("Notification settings updated successfully!");else throw new Error(v.error)}catch(i){c(i instanceof Error?i.message:"Failed to update notification settings")}finally{d(!1)}},P=()=>{var i,k;return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:((k=(i=o==null?void 0:o.name)==null?void 0:i.charAt(0))==null?void 0:k.toUpperCase())||"U"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(x,{variant:"secondary",size:"sm",onClick:()=>{const v=document.createElement("input");v.type="file",v.accept="image/*",v.onchange=U=>{var q;const X=(q=U.target.files)==null?void 0:q[0];X&&u({...f,avatar:X})},v.click()},children:[e.jsx(he,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(M,{label:"Full Name",value:f.name,onChange:v=>u({...f,name:v}),placeholder:"Enter your full name"}),e.jsx(M,{label:"Email Address",type:"email",value:f.email,onChange:v=>u({...f,email:v}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:f.bio,onChange:v=>u({...f,bio:v.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:y,isLoading:j,children:"Save Profile"})})]})})},D=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>h({...m,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${m.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(pe,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>h({...m,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${m.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(fe,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:m.language,onChange:i=>h({...m,language:i.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:()=>h({...m,defaultStudyMode:"flashcards"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${m.defaultStudyMode==="flashcards"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Flashcards"})}),e.jsx("button",{onClick:()=>h({...m,defaultStudyMode:"quiz"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${m.defaultStudyMode==="quiz"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Quiz"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),e.jsxs("select",{value:m.sessionDuration,onChange:i=>h({...m,sessionDuration:parseInt(i.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:45,children:"45 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:90,children:"1.5 hours"})]})]}),e.jsx(be,{value:m.difficultyLevel,onChange:i=>h({...m,difficultyLevel:i}),label:"Default Difficulty Level",className:"bg-background-secondary rounded-lg p-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>h({...m,studyReminders:!m.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>h({...m,autoSave:!m.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:I,isLoading:j,children:"Save Preferences"})})]})}),O=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(N).map(([i,k])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:i.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i==="emailNotifications"&&"Receive important updates via email",i==="studyReminders"&&"Get reminded when it's time to study",i==="weeklyProgress"&&"Weekly summary of your study progress",i==="marketingEmails"&&"Product updates and tips",i==="achievementNotifications"&&"Get notified when you unlock achievements",i==="streakReminders"&&"Reminders to maintain your study streak"]})]}),e.jsx("button",{onClick:()=>s({...N,[i]:!k}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${k?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${k?"translate-x-6":"translate-x-1"}`})})]},i))}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:T,isLoading:j,children:"Save Notification Settings"})})]})}),te=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(we,{enabled:t.twoFactorEnabled,onToggle:i=>r({...t,twoFactorEnabled:i})}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsx("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),e.jsx("button",{onClick:()=>r({...t,loginNotifications:!t.loginNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${t.loginNotifications?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${t.loginNotifications?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),e.jsxs("select",{value:t.sessionTimeout,onChange:i=>r({...t,sessionTimeout:parseInt(i.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:120,children:"2 hours"}),e.jsx("option",{value:480,children:"8 hours"}),e.jsx("option",{value:1440,children:"1 day"}),e.jsx("option",{value:10080,children:"1 week"}),e.jsx("option",{value:20160,children:"2 weeks"}),e.jsx("option",{value:30240,children:"3 weeks"}),e.jsx("option",{value:40320,children:"4 weeks"}),e.jsx("option",{value:50400,children:"5 weeks"}),e.jsx("option",{value:60480,children:"6 weeks"}),e.jsx("option",{value:70560,children:"7 weeks"}),e.jsx("option",{value:80640,children:"8 weeks"}),e.jsx("option",{value:0,children:"Never expire"})]})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(Q,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(x,{variant:"secondary",onClick:()=>w(!0),children:"Change Password"})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),e.jsx(x,{variant:"secondary",size:"sm",onClick:()=>{z("deactivate"),A(!0)},children:"Deactivate Account"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx(x,{variant:"danger",size:"sm",onClick:()=>{z("delete"),A(!0)},children:"Delete Account"})]})]})]})]})]})}),ae=()=>e.jsx(ye,{}),re=()=>e.jsx(Ne,{}),ne=()=>e.jsx(je,{}),ie=()=>{switch(a){case"profile":return P();case"preferences":return D();case"notifications":return O();case"security":return te();case"subscription":return ae();case"billing":return re();case"data":return ne();default:return P()}};return e.jsxs("div",{className:"min-h-screen bg-background-primary text-white",children:[e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),g&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:g}),e.jsx(x,{onClick:()=>c(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),b&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:b}),e.jsx(x,{onClick:()=>n(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:ke.map(i=>{const k=i.icon,v=a===i.id;return e.jsxs("button",{onClick:()=>S(i.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${v?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(k,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:i.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:i.description})]})]},i.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(V.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:ie()},a)})]})]}),e.jsx(ve,{isOpen:p,onClose:()=>w(!1),onSuccess:()=>{console.log("Password changed successfully")}}),e.jsx(Se,{isOpen:C,onClose:()=>A(!1),action:E})]})};export{Ae as SettingsPage};
