"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const creditController_1 = require("../controllers/creditController");
const router = (0, express_1.Router)();
// All credit routes require authentication
router.use(auth_1.authenticateToken);
// Credit information routes
router.get('/balance', creditController_1.getCreditBalance);
router.get('/history', creditController_1.getCreditHistory);
router.get('/stats', creditController_1.getCreditStats);
router.get('/pricing', creditController_1.getOperationCosts);
router.get('/check/:operationType', creditController_1.checkCreditSufficiency);
exports.default = router;
//# sourceMappingURL=credits.js.map