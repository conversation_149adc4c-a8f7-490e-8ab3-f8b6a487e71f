import{r as i,j as e,D as F,S as te,B as u,C as V,n as Z,L as oe,K as W,p as $,A as J,T as H,U as ae,E as R,i as K,V as Q,W as Y,H as U,d as L,X as ee,I as M,Y as X,_,b as de,Z as me,$ as xe,a0 as ue,a1 as he,y as pe,x as fe}from"./index-d4d58dfe.js";const se=[{id:"free",name:"Study Starter",price:0,interval:"month",features:["500 AI study generations per month","Basic flashcards and quizzes","Up to 5 document uploads","Basic study analytics","Perfect for trying out ChewyAI"]},{id:"pro_monthly",name:"Scholar Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Perfect for serious students"]},{id:"pro_yearly",name:"Academic Year Pass",price:99.99,interval:"year",features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Save $20 vs monthly (2 months free!)","Perfect for the full academic year"]}],ge=()=>{var x,I;const[a,C]=i.useState(null),[E,o]=i.useState(!0),[j,l]=i.useState(!1),[w,r]=i.useState(null);i.useEffect(()=>{h()},[]);const h=async()=>{o(!0),r(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch subscription data");const f=await t.json();if(f.success)C(f.data);else throw new Error(f.error)}catch(s){r(s instanceof Error?s.message:"Failed to load subscription data"),C({currentPlan:se[0],status:"active",nextBillingDate:new Date(Date.now()+30*24*60*60*1e3).toISOString()})}finally{o(!1)}},g=async s=>{l(!0),r(null);try{const t=localStorage.getItem("auth_token"),f=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({planId:s})});if(!f.ok)throw new Error("Failed to change subscription plan");const b=await f.json();if(b.success)await h();else throw new Error(b.error)}catch(t){r(t instanceof Error?t.message:"Failed to change subscription plan")}finally{l(!1)}},m=async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){l(!0),r(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to cancel subscription");const f=await t.json();if(f.success)await h();else throw new Error(f.error)}catch(s){r(s instanceof Error?s.message:"Failed to cancel subscription")}finally{l(!1)}}},p=async()=>{l(!0),r(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to reactivate subscription");const f=await t.json();if(f.success)await h();else throw new Error(f.error)}catch(s){r(s instanceof Error?s.message:"Failed to reactivate subscription")}finally{l(!1)}};return E?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(s=>e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"},s))})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),w&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:w})]}),a&&e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),e.jsx("p",{className:"text-gray-400",children:((x=a.currentPlan)==null?void 0:x.name)||"No active plan"})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a.status==="active"?"bg-green-500/20 text-green-400":a.status==="canceled"?"bg-red-500/20 text-red-400":a.status==="past_due"?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&e.jsxs("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[e.jsx(te,{className:"w-4 h-4"}),e.jsx("span",{children:a.cancelAtPeriodEnd?`Access ends on ${new Date(a.nextBillingDate).toLocaleDateString()}`:`Next billing date: ${new Date(a.nextBillingDate).toLocaleDateString()}`})]}),((I=a.currentPlan)==null?void 0:I.id)!=="free"&&e.jsxs("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?e.jsx(u,{onClick:p,isLoading:j,variant:"primary",size:"sm",children:"Reactivate Subscription"}):e.jsx(u,{onClick:m,isLoading:j,variant:"danger",size:"sm",children:"Cancel Subscription"}),e.jsxs(u,{onClick:h,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:se.map(s=>{var f;const t=((f=a==null?void 0:a.currentPlan)==null?void 0:f.id)===s.id;return e.jsxs(Z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":t?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"}`,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(oe,{className:"w-3 h-3"}),e.jsx("span",{children:"Most Popular"})]})}),t&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsxs("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(W,{className:"w-3 h-3"}),e.jsx("span",{children:"Current"})]})}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h5",{className:"text-xl font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.price,e.jsxs("span",{className:"text-lg text-gray-400",children:["/",s.interval]})]})]}),e.jsx("ul",{className:"space-y-3 mb-6",children:s.features.map((b,S)=>e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx(W,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-300 text-sm",children:b})]},S))}),e.jsx(u,{onClick:()=>g(s.id),disabled:t||j,isLoading:j,variant:s.popular?"primary":"secondary",className:"w-full",children:t?"Current Plan":`Switch to ${s.name}`})]},s.id)})})]})]})})},ye=()=>{const[a,C]=i.useState(!1),[E,o]=i.useState(!1),[j,l]=i.useState(!1),[w,r]=i.useState(null),[h,g]=i.useState(null),[m,p]=i.useState({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[x,I]=i.useState({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),s=i.useRef(null),t=async()=>{C(!0),r(null),g(null);try{const d=localStorage.getItem("auth_token"),c=await fetch("/api/data/export",{method:"POST",headers:{Authorization:`Bearer ${d}`,"Content-Type":"application/json"},body:JSON.stringify({exportData:x})});if(!c.ok)throw new Error("Failed to export data");const N=await c.blob(),y=window.URL.createObjectURL(N),k=document.createElement("a");k.href=y,k.download=`chewyai-data-export-${new Date().toISOString().split("T")[0]}.json`,k.click(),window.URL.revokeObjectURL(y),g("Data exported successfully!")}catch(d){r(d instanceof Error?d.message:"Failed to export data")}finally{C(!1)}},f=async d=>{o(!0),r(null),g(null);try{const c=new FormData;c.append("file",d);const N=localStorage.getItem("auth_token"),y=await fetch("/api/data/import",{method:"POST",headers:{Authorization:`Bearer ${N}`},body:c});if(!y.ok)throw new Error("Failed to import data");const k=await y.json();if(k.success)g(`Data imported successfully! ${k.imported} items processed.`),await P();else throw new Error(k.error)}catch(c){r(c instanceof Error?c.message:"Failed to import data")}finally{o(!1)}},b=d=>{var N;const c=(N=d.target.files)==null?void 0:N[0];if(c){if(c.type!=="application/json"){r("Please select a valid JSON file");return}f(c)}},S=async d=>{if(confirm(`Are you sure you want to clear all ${d}? This action cannot be undone.`)){l(!0),r(null),g(null);try{const c=localStorage.getItem("auth_token"),N=await fetch(`/api/data/clear/${d}`,{method:"DELETE",headers:{Authorization:`Bearer ${c}`}});if(!N.ok)throw new Error(`Failed to clear ${d}`);const y=await N.json();if(y.success)g(`${d} cleared successfully!`),await P();else throw new Error(y.error)}catch(c){r(c instanceof Error?c.message:`Failed to clear ${d}`)}finally{l(!1)}}},P=async()=>{try{const d=localStorage.getItem("auth_token"),c=await fetch("/api/data/stats",{headers:{Authorization:`Bearer ${d}`}});if(c.ok){const N=await c.json();N.success&&p(N.data)}}catch{}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),w&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:w})]}),h&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:h})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),e.jsxs(u,{onClick:P,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.studySets}),e.jsx("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.flashcards}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.quizzes}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.documents}),e.jsx("div",{className:"text-sm text-gray-400",children:"Documents"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.totalSize}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(J,{className:"w-6 h-6 text-blue-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsx("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(x).map(([d,c])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm text-gray-300 capitalize",children:d.replace(/([A-Z])/g," $1").trim()}),e.jsx("button",{onClick:()=>I({...x,[d]:!c}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${c?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${c?"translate-x-6":"translate-x-1"}`})})]},d))]}),e.jsxs(u,{onClick:t,isLoading:a,variant:"primary",children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(H,{className:"w-6 h-6 text-green-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Import Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Import data from a previously exported JSON file. This will merge with your existing data."}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(te,{className:"w-5 h-5 text-yellow-400"}),e.jsx("span",{className:"text-yellow-400 font-medium",children:"Important"})]}),e.jsx("p",{className:"text-yellow-300 text-sm mt-1",children:"Importing data will merge with your existing data. Duplicate items may be created."})]}),e.jsx("input",{ref:s,type:"file",accept:".json",onChange:b,className:"hidden"}),e.jsxs(u,{onClick:()=>{var d;return(d=s.current)==null?void 0:d.click()},isLoading:E,variant:"secondary",children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Select File to Import"]})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(ae,{className:"w-6 h-6 text-red-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(u,{onClick:()=>S("study-sets"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),e.jsx(u,{onClick:()=>S("flashcards"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(u,{onClick:()=>S("quizzes"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),e.jsx(u,{onClick:()=>S("analytics"),isLoading:j,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},be=()=>{const[a,C]=i.useState(null),[E,o]=i.useState(!0),[j,l]=i.useState(!1),[w,r]=i.useState(null);i.useEffect(()=>{h()},[]);const h=async()=>{o(!0),r(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/billing",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch billing data");const f=await t.json();if(f.success)C(f.data);else throw new Error(f.error)}catch(s){r(s instanceof Error?s.message:"Failed to load billing data"),C({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-30*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:new Date().toISOString(),dueDate:new Date(Date.now()+7*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+30*24*60*60*1e3).toISOString()}})}finally{o(!1)}},g=async s=>{try{const t=localStorage.getItem("auth_token"),f=await fetch(`/api/billing/invoices/${s}/download`,{headers:{Authorization:`Bearer ${t}`}});if(!f.ok)throw new Error("Failed to download invoice");const b=await f.blob(),S=window.URL.createObjectURL(b),P=document.createElement("a");P.href=S,P.download=`invoice-${s}.pdf`,P.click(),window.URL.revokeObjectURL(S)}catch(t){r(t instanceof Error?t.message:"Failed to download invoice")}},m=async()=>{r("Payment method management coming soon")},p=async s=>{l(!0),r(null);try{const t=localStorage.getItem("auth_token"),f=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:s})});if(!f.ok)throw new Error("Failed to update default payment method");const b=await f.json();if(b.success)await h();else throw new Error(b.error)}catch(t){r(t instanceof Error?t.message:"Failed to update payment method")}finally{l(!1)}},x=s=>{switch(s){case"paid":return e.jsx($,{className:"w-5 h-5 text-green-400"});case"pending":return e.jsx(K,{className:"w-5 h-5 text-yellow-400"});case"failed":return e.jsx(Q,{className:"w-5 h-5 text-red-400"});default:return e.jsx(K,{className:"w-5 h-5 text-gray-400"})}},I=s=>{switch(s){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return E?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded-lg"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),e.jsxs(u,{onClick:h,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),w&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:w})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),e.jsx(u,{onClick:m,variant:"secondary",size:"sm",children:"Add Payment Method"})]}),(a==null?void 0:a.paymentMethods.length)===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(R,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"No payment methods added"})]}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.paymentMethods.map(s=>{var t;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(R,{className:"w-6 h-6 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-white font-medium",children:[(t=s.brand)==null?void 0:t.toUpperCase()," •••• ",s.last4]}),s.isDefault&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),s.expiryMonth&&s.expiryYear&&e.jsxs("p",{className:"text-gray-400 text-sm",children:["Expires ",s.expiryMonth.toString().padStart(2,"0"),"/",s.expiryYear]})]})]}),!s.isDefault&&e.jsx(u,{onClick:()=>p(s.id),isLoading:j,variant:"secondary",size:"sm",children:"Set as Default"})]},s.id)})})]}),(a==null?void 0:a.nextInvoice)&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("p",{className:"text-blue-300",children:["$",a.nextInvoice.amount," ",a.nextInvoice.currency.toUpperCase()]}),e.jsxs("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(a.nextInvoice.date).toLocaleDateString()]})]})})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),(a==null?void 0:a.invoices.length)===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-400",children:"No invoices found"})}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.invoices.map(s=>e.jsxs(Z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[x(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-white font-medium",children:s.number}),e.jsx("span",{className:`text-sm font-medium ${I(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description}),e.jsxs("p",{className:"text-gray-500 text-xs",children:[new Date(s.date).toLocaleDateString(),s.dueDate&&s.status==="pending"&&e.jsxs("span",{children:[" • Due ",new Date(s.dueDate).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:["$",s.amount," ",s.currency.toUpperCase()]}),s.status==="paid"&&e.jsxs(u,{onClick:()=>g(s.id),variant:"secondary",size:"sm",children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},s.id))})]})]})})},je=({enabled:a,onToggle:C})=>{const[E,o]=i.useState(!1),[j,l]=i.useState(!1),[w,r]=i.useState(null),[h,g]=i.useState(null),[m,p]=i.useState(null),[x,I]=i.useState(""),[s,t]=i.useState(!1),[f,b]=i.useState(!1),S=async()=>{l(!0),r(null),g(null);try{const{createClient:y}=await _(()=>import("./index-d4d58dfe.js").then(z=>z.a2),["assets/index-d4d58dfe.js","assets/index-f516001c.css"]),k=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:T,error:D}=await k.auth.mfa.enroll({factorType:"totp",friendlyName:"ChewyAI Authenticator"});if(D)throw new Error(D.message);p({qrCode:T.totp.qr_code,secret:T.totp.secret,backupCodes:[],factorId:T.id}),o(!0)}catch(y){r(y instanceof Error?y.message:"Failed to setup 2FA"),p({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",factorId:"mock-factor-id",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),o(!0)}finally{l(!1)}},P=async()=>{if(!x||x.length!==6){r("Please enter a valid 6-digit code");return}l(!0),r(null);try{const{createClient:y}=await _(()=>import("./index-d4d58dfe.js").then(O=>O.a2),["assets/index-d4d58dfe.js","assets/index-f516001c.css"]),k=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI");if(!m)throw new Error("No setup data available");const{data:T,error:D}=await k.auth.mfa.challenge({factorId:m.factorId});if(D)throw new Error(D.message);const{error:z}=await k.auth.mfa.verify({factorId:m.factorId,challengeId:T.id,code:x});if(z)throw new Error(z.message);g("Two-factor authentication enabled successfully!"),o(!1),b(!0),C(!0)}catch(y){r(y instanceof Error?y.message:"Failed to verify 2FA code")}finally{l(!1)}},d=async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){l(!0),r(null);try{const{createClient:y}=await _(()=>import("./index-d4d58dfe.js").then(z=>z.a2),["assets/index-d4d58dfe.js","assets/index-f516001c.css"]),k=y("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:T,error:D}=await k.auth.mfa.listFactors();if(D)throw new Error(D.message);for(const z of T.totp){const{error:O}=await k.auth.mfa.unenroll({factorId:z.id});O&&console.error("Failed to unenroll factor:",O)}g("Two-factor authentication disabled successfully"),C(!1)}catch(y){r(y instanceof Error?y.message:"Failed to disable 2FA")}finally{l(!1)}}},c=y=>{navigator.clipboard.writeText(y),g("Copied to clipboard!"),setTimeout(()=>g(null),2e3)},N=()=>{o(!1),p(null),I(""),r(null),g(null)};return E&&m?e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx(Y,{className:"w-6 h-6 text-primary-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),w&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:w})]})}),h&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:h})]})}),f?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx($,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),e.jsx("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),e.jsx("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),e.jsx("div",{className:"grid grid-cols-2 gap-2 mb-4",children:m.backupCodes.map((y,k)=>e.jsx("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:e.jsx("code",{className:"text-primary-400 font-mono",children:y})},k))}),e.jsxs(u,{onClick:()=>c(m.backupCodes.join(`
`)),variant:"secondary",size:"sm",children:[e.jsx(ee,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),e.jsx(u,{onClick:()=>b(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block",children:e.jsx("img",{src:m.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:e.jsx("code",{className:"text-primary-400 font-mono",children:s?m.secret:"••••••••••••••••"})}),e.jsx(u,{onClick:()=>t(!s),variant:"secondary",size:"sm",children:s?e.jsx(U,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})}),e.jsx(u,{onClick:()=>c(m.secret),variant:"secondary",size:"sm",children:e.jsx(ee,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(M,{value:x,onChange:I,placeholder:"123456",className:"flex-1"}),e.jsx(u,{onClick:P,isLoading:j,disabled:x.length!==6,children:"Verify"})]})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsx(u,{onClick:N,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Y,{className:`w-6 h-6 ${a?"text-green-400":"text-gray-400"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a?"Enabled":"Disabled"})]}),w&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:w})]})}),h&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:h})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-300 text-sm",children:a?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),e.jsx("div",{className:"flex space-x-3",children:a?e.jsx(u,{onClick:d,isLoading:j,variant:"danger",children:"Disable 2FA"}):e.jsxs(u,{onClick:S,isLoading:j,variant:"primary",children:[e.jsx(X,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},we=({isOpen:a,onClose:C,onSuccess:E})=>{const[o,j]=i.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[l,w]=i.useState({current:!1,new:!1,confirm:!1}),[r,h]=i.useState(!1),[g,m]=i.useState(null),[p,x]=i.useState(null),I=(d,c)=>{j(N=>({...N,[d]:c})),m(null)},s=d=>c=>{I(d,c)},t=d=>{w(c=>({...c,[d]:!c[d]}))},f=()=>o.currentPassword?o.newPassword?o.newPassword.length<8?(m("New password must be at least 8 characters long"),!1):o.newPassword!==o.confirmPassword?(m("New passwords do not match"),!1):o.currentPassword===o.newPassword?(m("New password must be different from current password"),!1):!0:(m("New password is required"),!1):(m("Current password is required"),!1),b=async d=>{if(d.preventDefault(),!!f()){h(!0),m(null),x(null);try{const c=localStorage.getItem("auth_token"),N=await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`},body:JSON.stringify({currentPassword:o.currentPassword,newPassword:o.newPassword})}),y=await N.json();if(!N.ok)throw new Error(y.error||"Failed to change password");x("Password changed successfully!"),setTimeout(()=>{E(),C(),S()},1500)}catch(c){m(c instanceof Error?c.message:"Failed to change password")}finally{h(!1)}}},S=()=>{j({currentPassword:"",newPassword:"",confirmPassword:""}),w({current:!1,new:!1,confirm:!1}),m(null),x(null)},P=()=>{S(),C()};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(X,{className:"w-5 h-5 text-primary-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Change Password"})]}),e.jsx("button",{onClick:P,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Q,{className:"w-5 h-5"})})]}),e.jsxs("form",{onSubmit:b,className:"p-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:l.current?"text":"password",value:o.currentPassword,onChange:s("currentPassword"),placeholder:"Enter your current password",className:"pr-10",disabled:r}),e.jsx("button",{type:"button",onClick:()=>t("current"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:l.current?e.jsx(U,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:l.new?"text":"password",value:o.newPassword,onChange:s("newPassword"),placeholder:"Enter your new password",className:"pr-10",disabled:r}),e.jsx("button",{type:"button",onClick:()=>t("new"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:l.new?e.jsx(U,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{type:l.confirm?"text":"password",value:o.confirmPassword,onChange:s("confirmPassword"),placeholder:"Confirm your new password",className:"pr-10",disabled:r}),e.jsx("button",{type:"button",onClick:()=>t("confirm"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:l.confirm?e.jsx(U,{className:"w-4 h-4"}):e.jsx(L,{className:"w-4 h-4"})})]})]}),g&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:g})}),p&&e.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-green-400 text-sm",children:p})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(u,{type:"button",variant:"secondary",onClick:P,disabled:r,className:"flex-1",children:"Cancel"}),e.jsx(u,{type:"submit",variant:"primary",isLoading:r,disabled:r,className:"flex-1",children:r?"Changing...":"Change Password"})]})]})]})}):null},Ne=({isOpen:a,onClose:C,action:E})=>{const[o,j]=i.useState(""),[l,w]=i.useState(!1),[r,h]=i.useState(null),[g,m]=i.useState("confirm"),x={deactivate:{title:"Deactivate Account",description:"Your account will be temporarily disabled. You can reactivate it by logging in again.",confirmText:"DEACTIVATE",buttonText:"Deactivate Account",warningText:"This will temporarily disable your account and log you out.",endpoint:"/api/auth/deactivate-account"},delete:{title:"Delete Account",description:"This will permanently delete your account and all associated data. This action cannot be undone.",confirmText:"DELETE FOREVER",buttonText:"Delete Account Forever",warningText:"This will permanently delete all your data including study sets, flashcards, progress, and subscription information.",endpoint:"/api/auth/delete-account"}}[E],I=o===x.confirmText,s=async()=>{if(g==="confirm"){m("final");return}if(!I){h(`Please type "${x.confirmText}" to confirm`);return}w(!0),h(null);try{const b=localStorage.getItem("auth_token"),S=await fetch(x.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b}`},body:JSON.stringify({confirmation:o})}),P=await S.json();if(!S.ok)throw new Error(P.error||`Failed to ${E} account`);localStorage.removeItem("auth_token"),localStorage.removeItem("user_data"),window.location.href="/login"}catch(b){h(b instanceof Error?b.message:`Failed to ${E} account`)}finally{w(!1)}},t=()=>{j(""),h(null),m("confirm"),C()},f=()=>{m("confirm"),h(null)};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:x.title})]}),e.jsx("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Q,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6",children:g==="confirm"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-400 mb-2",children:"Warning"}),e.jsx("p",{className:"text-gray-300 text-sm",children:x.warningText})]})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-300 text-sm mb-4",children:x.description}),E==="delete"&&e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("p",{children:"This will delete:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"All study sets and flashcards"}),e.jsx("li",{children:"Quiz history and progress"}),e.jsx("li",{children:"Account settings and preferences"}),e.jsx("li",{children:"Subscription and billing information"}),e.jsx("li",{children:"All uploaded documents"})]})]})]}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(u,{variant:"secondary",onClick:t,className:"flex-1",children:"Cancel"}),e.jsx(u,{variant:"danger",onClick:s,className:"flex-1",children:"Continue"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(ae,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-red-400",children:"Final Confirmation"})]})}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-300 text-sm mb-4",children:["To confirm this action, please type ",e.jsx("span",{className:"font-mono font-bold text-red-400",children:x.confirmText})," in the box below:"]}),e.jsx(M,{type:"text",value:o,onChange:b=>{j(b),h(null)},placeholder:x.confirmText,className:"font-mono",disabled:l})]}),r&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:r})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(u,{variant:"secondary",onClick:f,disabled:l,className:"flex-1",children:"Back"}),e.jsx(u,{variant:"danger",onClick:s,isLoading:l,disabled:l||!I,className:"flex-1",children:l?"Processing...":x.buttonText})]})]})})]})}):null},ve=[{id:"profile",label:"Profile",icon:me,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:xe,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:ue,description:"Control notification settings"},{id:"security",label:"Security",icon:Y,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:R,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:R,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:he,description:"Export, import, and manage your data"}],ke=()=>{const[a,C]=i.useState("profile"),[E,o]=i.useState(!1),[j,l]=i.useState(null),[w,r]=i.useState(null),{user:h}=de(),[g,m]=i.useState({name:(h==null?void 0:h.name)||"",email:(h==null?void 0:h.email)||"",bio:"",avatar:null}),[p,x]=i.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:"medium"}),[I,s]=i.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[t,f]=i.useState({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}),[b,S]=i.useState(!1),[P,d]=i.useState(!1),[c,N]=i.useState("deactivate");i.useEffect(()=>{(async()=>{try{o(!0)}catch{l("Failed to load user settings")}finally{o(!1)}})()},[]);const y=async()=>{o(!0),l(null),r(null);try{const n=new FormData;n.append("name",g.name),n.append("bio",g.bio),g.avatar&&n.append("avatar",g.avatar);const A=localStorage.getItem("auth_token"),v=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:`Bearer ${A}`},body:n});if(!v.ok)throw new Error("Failed to update profile");const B=await v.json();if(B.success)r("Profile updated successfully!");else throw new Error(B.error)}catch(n){l(n instanceof Error?n.message:"Failed to update profile")}finally{o(!1)}},k=async()=>{o(!0),l(null),r(null);try{const n=localStorage.getItem("auth_token"),A=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify(p)});if(!A.ok)throw new Error("Failed to update preferences");const v=await A.json();if(v.success)r("Preferences updated successfully!");else throw new Error(v.error)}catch(n){l(n instanceof Error?n.message:"Failed to update preferences")}finally{o(!1)}},T=async()=>{o(!0),l(null),r(null);try{const n=localStorage.getItem("auth_token"),A=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},body:JSON.stringify(I)});if(!A.ok)throw new Error("Failed to update notification settings");const v=await A.json();if(v.success)r("Notification settings updated successfully!");else throw new Error(v.error)}catch(n){l(n instanceof Error?n.message:"Failed to update notification settings")}finally{o(!1)}},D=()=>{var n,A;return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:((A=(n=h==null?void 0:h.name)==null?void 0:n.charAt(0))==null?void 0:A.toUpperCase())||"U"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(u,{variant:"secondary",size:"sm",onClick:()=>{const v=document.createElement("input");v.type="file",v.accept="image/*",v.onchange=B=>{var G;const q=(G=B.target.files)==null?void 0:G[0];q&&m({...g,avatar:q})},v.click()},children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(M,{label:"Full Name",value:g.name,onChange:v=>m({...g,name:v}),placeholder:"Enter your full name"}),e.jsx(M,{label:"Email Address",type:"email",value:g.email,onChange:v=>m({...g,email:v}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:g.bio,onChange:v=>m({...g,bio:v.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(u,{onClick:y,isLoading:E,children:"Save Profile"})})]})})},z=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>x({...p,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${p.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(pe,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>x({...p,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${p.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(fe,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:p.language,onChange:n=>x({...p,language:n.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:()=>x({...p,defaultStudyMode:"flashcards"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${p.defaultStudyMode==="flashcards"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Flashcards"})}),e.jsx("button",{onClick:()=>x({...p,defaultStudyMode:"quiz"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${p.defaultStudyMode==="quiz"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Quiz"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),e.jsxs("select",{value:p.sessionDuration,onChange:n=>x({...p,sessionDuration:parseInt(n.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:45,children:"45 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:90,children:"1.5 hours"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Difficulty Level"}),e.jsx("div",{className:"flex space-x-4",children:["easy","medium","hard"].map(n=>e.jsx("button",{onClick:()=>x({...p,difficultyLevel:n}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${p.difficultyLevel===n?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{className:"capitalize",children:n})},n))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>x({...p,studyReminders:!p.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>x({...p,autoSave:!p.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(u,{onClick:k,isLoading:E,children:"Save Preferences"})})]})}),O=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(I).map(([n,A])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:n.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[n==="emailNotifications"&&"Receive important updates via email",n==="studyReminders"&&"Get reminded when it's time to study",n==="weeklyProgress"&&"Weekly summary of your study progress",n==="marketingEmails"&&"Product updates and tips",n==="achievementNotifications"&&"Get notified when you unlock achievements",n==="streakReminders"&&"Reminders to maintain your study streak"]})]}),e.jsx("button",{onClick:()=>s({...I,[n]:!A}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${A?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${A?"translate-x-6":"translate-x-1"}`})})]},n))}),e.jsx("div",{className:"mt-6",children:e.jsx(u,{onClick:T,isLoading:E,children:"Save Notification Settings"})})]})}),re=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(je,{enabled:t.twoFactorEnabled,onToggle:n=>f({...t,twoFactorEnabled:n})}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsx("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),e.jsx("button",{onClick:()=>f({...t,loginNotifications:!t.loginNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${t.loginNotifications?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${t.loginNotifications?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),e.jsxs("select",{value:t.sessionTimeout,onChange:n=>f({...t,sessionTimeout:parseInt(n.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:120,children:"2 hours"}),e.jsx("option",{value:480,children:"8 hours"}),e.jsx("option",{value:1440,children:"1 day"}),e.jsx("option",{value:10080,children:"1 week"}),e.jsx("option",{value:20160,children:"2 weeks"}),e.jsx("option",{value:30240,children:"3 weeks"}),e.jsx("option",{value:40320,children:"4 weeks"}),e.jsx("option",{value:50400,children:"5 weeks"}),e.jsx("option",{value:60480,children:"6 weeks"}),e.jsx("option",{value:70560,children:"7 weeks"}),e.jsx("option",{value:80640,children:"8 weeks"}),e.jsx("option",{value:0,children:"Never expire"})]})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(X,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(u,{variant:"secondary",onClick:()=>S(!0),children:"Change Password"})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),e.jsx(u,{variant:"secondary",size:"sm",onClick:()=>{N("deactivate"),d(!0)},children:"Deactivate Account"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx(u,{variant:"danger",size:"sm",onClick:()=>{N("delete"),d(!0)},children:"Delete Account"})]})]})]})]})]})}),ne=()=>e.jsx(ge,{}),ie=()=>e.jsx(be,{}),le=()=>e.jsx(ye,{}),ce=()=>{switch(a){case"profile":return D();case"preferences":return z();case"notifications":return O();case"security":return re();case"subscription":return ne();case"billing":return ie();case"data":return le();default:return D()}};return e.jsxs("div",{className:"min-h-screen bg-background-primary text-white",children:[e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),j&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:j}),e.jsx(u,{onClick:()=>l(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),w&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:w}),e.jsx(u,{onClick:()=>r(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:ve.map(n=>{const A=n.icon,v=a===n.id;return e.jsxs("button",{onClick:()=>C(n.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${v?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(A,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:n.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:n.description})]})]},n.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(Z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:ce()},a)})]})]}),e.jsx(we,{isOpen:b,onClose:()=>S(!1),onSuccess:()=>{console.log("Password changed successfully")}}),e.jsx(Ne,{isOpen:P,onClose:()=>d(!1),action:c})]})};export{ke as SettingsPage};
