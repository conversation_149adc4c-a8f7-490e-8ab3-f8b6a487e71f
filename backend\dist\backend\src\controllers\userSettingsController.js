"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserPreferences = exports.getUserPreferences = exports.updateUserSettings = exports.getUserSettings = void 0;
const userSettingsService_1 = require("../services/userSettingsService");
const types_1 = require("../../../shared/types");
const getUserSettings = async (req, res) => {
    try {
        const userId = req.user.id;
        const settings = await userSettingsService_1.userSettingsService.getUserSettings(userId);
        res.json({
            success: true,
            data: settings
        });
    }
    catch (error) {
        console.error('Get user settings error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to fetch user settings'
        });
    }
};
exports.getUserSettings = getUserSettings;
const updateUserSettings = async (req, res) => {
    try {
        const userId = req.user.id;
        const updates = req.body;
        // Validate request body
        if (!updates || typeof updates !== 'object') {
            return res.status(400).json({
                success: false,
                error: 'Invalid request body'
            });
        }
        // Validate allowed fields
        const allowedFields = ['skip_delete_confirmations', 'shuffle_flashcards'];
        const providedFields = Object.keys(updates);
        const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
        if (invalidFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Invalid fields: ${invalidFields.join(', ')}`
            });
        }
        if (providedFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No valid fields provided for update'
            });
        }
        const updatedSettings = await userSettingsService_1.userSettingsService.updateUserSettings(userId, updates);
        res.json({
            success: true,
            data: updatedSettings
        });
    }
    catch (error) {
        console.error('Update user settings error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to update user settings'
        });
    }
};
exports.updateUserSettings = updateUserSettings;
const getUserPreferences = async (_req, res) => {
    try {
        // For now, return default preferences
        // In a full implementation, you would fetch user-specific preferences from the database
        // using req.user!.id
        const preferences = {
            theme: 'dark',
            language: 'en',
            studyReminders: true,
            autoSave: true,
            defaultStudyMode: 'flashcards',
            sessionDuration: 30,
            difficultyLevel: types_1.DifficultyLevel.MEDIUM
        };
        res.json({
            success: true,
            data: preferences
        });
    }
    catch (error) {
        console.error('Get user preferences error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to fetch user preferences'
        });
    }
};
exports.getUserPreferences = getUserPreferences;
const updateUserPreferences = async (req, res) => {
    try {
        const preferences = req.body;
        // Note: req.user!.id would be used here to save preferences to database in full implementation
        // Validate request body
        if (!preferences || typeof preferences !== 'object') {
            return res.status(400).json({
                success: false,
                error: 'Invalid request body'
            });
        }
        // Validate allowed fields
        const allowedFields = ['theme', 'language', 'studyReminders', 'autoSave', 'defaultStudyMode', 'sessionDuration', 'difficultyLevel'];
        const providedFields = Object.keys(preferences);
        const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
        if (invalidFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Invalid fields: ${invalidFields.join(', ')}`
            });
        }
        if (providedFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No valid fields provided for update'
            });
        }
        // Validate difficulty level if provided
        if (preferences.difficultyLevel && !Object.values(types_1.DifficultyLevel).includes(preferences.difficultyLevel)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid difficulty level'
            });
        }
        // For now, just return success with the updated preferences
        // In a real implementation, you would save these to the database
        const updatedPreferences = {
            theme: preferences.theme || 'dark',
            language: preferences.language || 'en',
            studyReminders: preferences.studyReminders !== undefined ? preferences.studyReminders : true,
            autoSave: preferences.autoSave !== undefined ? preferences.autoSave : true,
            defaultStudyMode: preferences.defaultStudyMode || 'flashcards',
            sessionDuration: preferences.sessionDuration || 30,
            difficultyLevel: preferences.difficultyLevel || types_1.DifficultyLevel.MEDIUM
        };
        res.json({
            success: true,
            data: updatedPreferences
        });
    }
    catch (error) {
        console.error('Update user preferences error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to update user preferences'
        });
    }
};
exports.updateUserPreferences = updateUserPreferences;
//# sourceMappingURL=userSettingsController.js.map